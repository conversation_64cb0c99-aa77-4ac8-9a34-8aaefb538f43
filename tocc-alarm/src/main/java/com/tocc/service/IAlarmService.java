package com.tocc.service;

import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.AlarmInfoVO;

import java.util.List;

/**
 * 告警信息Service接口
 * 
 * <AUTHOR>
 */
public interface IAlarmService {
    
    /**
     * 查询告警信息
     * 
     * @param alarmId 告警信息主键
     * @return 告警信息
     */
    AlarmInfoVO selectAlarmInfoByAlarmId(String alarmId);

    /**
     * 查询告警信息列表
     * 
     * @param alarmInfo 告警信息
     * @return 告警信息集合
     */
    List<AlarmInfoVO> selectAlarmInfoList(AlarmInfoDTO alarmInfo);

    /**
     * 新增告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    int insertAlarmInfo(AlarmInfoDTO alarmInfo);

    /**
     * 修改告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    int updateAlarmInfo(AlarmInfoDTO alarmInfo);

    /**
     * 批量删除告警信息
     * 
     * @param alarmIds 需要删除的告警信息主键集合
     * @return 结果
     */
    int deleteAlarmInfoByAlarmIds(String[] alarmIds);

    /**
     * 删除告警信息信息
     * 
     * @param alarmId 告警信息主键
     * @return 结果
     */
    int deleteAlarmInfoByAlarmId(String alarmId);

    /**
     * 处理告警
     * 
     * @param alarmId 告警ID
     * @param status 处理状态（1已处理 2已忽略）
     * @param processResult 处理结果
     * @return 结果
     */
    int processAlarm(String alarmId, String status, String processResult);

    /**
     * 统计告警数量
     * 
     * @param alarmInfo 查询条件
     * @return 统计数量
     */
    int countAlarmInfo(AlarmInfoDTO alarmInfo);



    /**
     * 检查是否存在相同的超时告警
     *
     * @param infoType 信息类型
     * @param infoId 信息ID
     * @return 是否存在
     */
    boolean existsTimeoutAlarm(String infoType, String infoId);

    /**
     * 创建告警
     *
     * @param alarmInfo 告警信息
     * @return 结果
     */
    int createAlarm(AlarmInfoDTO alarmInfo);
}
