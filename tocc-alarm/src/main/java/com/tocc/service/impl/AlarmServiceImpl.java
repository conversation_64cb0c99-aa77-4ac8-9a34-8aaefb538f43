package com.tocc.service.impl;

import com.tocc.common.annotation.DataScope;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.AlarmInfoVO;
import com.tocc.mapper.AlarmInfoMapper;
import com.tocc.service.IAlarmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 告警信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class AlarmServiceImpl implements IAlarmService {
    
    @Autowired
    private AlarmInfoMapper alarmInfoMapper;

    @Autowired
    private IOrganizationService organizationService;

    /**
     * 查询告警信息
     * 
     * @param alarmId 告警信息主键
     * @return 告警信息
     */
    @Override
    public AlarmInfoVO selectAlarmInfoByAlarmId(String alarmId) {
        return alarmInfoMapper.selectAlarmInfoByAlarmId(alarmId);
    }

    /**
     * 查询告警信息列表
     *
     * @param alarmInfo 告警信息
     * @return 告警信息
     */
    @Override
    @DataScope(deptAlias = "a")
    public List<AlarmInfoVO> selectAlarmInfoList(AlarmInfoDTO alarmInfo) {
        return alarmInfoMapper.selectAlarmInfoList(alarmInfo);
    }

    /**
     * 新增告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int insertAlarmInfo(AlarmInfoDTO alarmInfo) {
        // 设置默认值
        if (alarmInfo.getAlarmId() == null || alarmInfo.getAlarmId().trim().isEmpty()) {
            alarmInfo.setAlarmId(IdUtils.fastSimpleUUID());
        }
        if (alarmInfo.getAlarmTime() == null) {
            alarmInfo.setAlarmTime(new Date());
        }
        if (alarmInfo.getStatus() == null || alarmInfo.getStatus().trim().isEmpty()) {
            alarmInfo.setStatus("0"); // 默认未处理
        }
        
        alarmInfo.setCreateBy(SecurityUtils.getUsername());
        alarmInfo.setCreateTime(DateUtils.getNowDate());
        
        return alarmInfoMapper.insertAlarmInfo(alarmInfo);
    }

    /**
     * 修改告警信息
     * 
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int updateAlarmInfo(AlarmInfoDTO alarmInfo) {
        alarmInfo.setUpdateBy(SecurityUtils.getUsername());
        alarmInfo.setUpdateTime(DateUtils.getNowDate());
        return alarmInfoMapper.updateAlarmInfo(alarmInfo);
    }

    /**
     * 批量删除告警信息
     * 
     * @param alarmIds 需要删除的告警信息主键
     * @return 结果
     */
    @Override
    public int deleteAlarmInfoByAlarmIds(String[] alarmIds) {
        return alarmInfoMapper.deleteAlarmInfoByAlarmIds(alarmIds);
    }

    /**
     * 删除告警信息信息
     * 
     * @param alarmId 告警信息主键
     * @return 结果
     */
    @Override
    public int deleteAlarmInfoByAlarmId(String alarmId) {
        return alarmInfoMapper.deleteAlarmInfoByAlarmId(alarmId);
    }

    /**
     * 处理告警
     * 
     * @param alarmId 告警ID
     * @param status 处理状态（1已处理 2已忽略）
     * @param processResult 处理结果
     * @return 结果
     */
    @Override
    public int processAlarm(String alarmId, String status, String processResult) {
        String processorId = SecurityUtils.getUserId().toString();
        String processorName = SecurityUtils.getUsername();
        String updateBy = SecurityUtils.getUsername();
        
        return alarmInfoMapper.updateAlarmStatus(alarmId, status, processorId, 
                                               processorName, processResult, updateBy);
    }

    /**
     * 统计告警数量
     * 
     * @param alarmInfo 查询条件
     * @return 统计数量
     */
    @Override
    public int countAlarmInfo(AlarmInfoDTO alarmInfo) {
        return alarmInfoMapper.countAlarmInfo(alarmInfo);
    }



    /**
     * 检查是否存在相同的超时告警
     *
     * @param infoType 信息类型
     * @param infoId 信息ID
     * @return 是否存在
     */
    @Override
    public boolean existsTimeoutAlarm(String infoType, String infoId) {
        // 查询最近7天内是否已有相同的超时告警
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);

        return alarmInfoMapper.existsTimeoutAlarm(infoType, infoId, sevenDaysAgo);
    }

    /**
     * 创建告警
     *
     * @param alarmInfo 告警信息
     * @return 结果
     */
    @Override
    public int createAlarm(AlarmInfoDTO alarmInfo) {
        return insertAlarmInfo(alarmInfo);
    }
}
