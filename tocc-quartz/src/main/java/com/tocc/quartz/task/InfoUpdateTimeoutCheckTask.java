package com.tocc.quartz.task;

import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.em.service.IEmPrePlanService;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.service.IAlarmService;
import com.tocc.service.IRescueTeamService;
import com.tocc.service.IWarehouseService;
import com.tocc.system.service.ISysDictDataService;
import com.tocc.system.service.ISysUserService;
import com.tocc.system.service.ISysDeptService;
import com.tocc.system.service.IExpertService;
import com.tocc.system.domain.vo.ExpertInfoVO;
import com.tocc.domain.vo.YkTokenVO;
import com.tocc.mapper.AlarmInfoMapper;
import com.tocc.quartz.domain.SysJob;
import com.tocc.quartz.domain.dto.InfoTimeoutTaskConfigDTO;
import com.tocc.quartz.service.ISysJobService;
import com.tocc.quartz.service.IInfoTimeoutTaskConfigService;
import com.google.gson.Gson;
import com.tocc.utils.HttpClientUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Calendar;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 信息更新超时检查定时任务
 * 
 * <AUTHOR>
 */
@Component("infoUpdateTimeoutCheckTask")
public class InfoUpdateTimeoutCheckTask {
    
    private static final Logger log = LoggerFactory.getLogger(InfoUpdateTimeoutCheckTask.class);
    
    @Autowired
    private IAlarmService alarmService;

    @Autowired
    private AlarmInfoMapper alarmInfoMapper;

    @Autowired
    private ISysJobService jobService;

    @Autowired
    private IInfoTimeoutTaskConfigService taskConfigService;

    @Autowired
    private ISysDictDataService dictDataService;

    // 超时阈值配置（分钟，从天数配置转换而来）
    private int prePlanTimeoutMinutes;
    private int rescueTeamTimeoutMinutes;
    private int warehouseTimeoutMinutes;
    private int expertTimeoutMinutes;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;
    
    @Autowired
    private IEmPrePlanService prePlanService;
    
    @Autowired
    private IRescueTeamService rescueTeamService;
    
    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IExpertService expertService;
    
    /**
     * 执行信息更新超时检查（无参版本）
     */
    public void execute() {
        execute(null);
    }

    /**
     * 执行信息更新超时检查
     *
     * @param params 参数（可选）
     */
    public void execute(String params) {
        log.info("开始执行信息更新超时检查任务，参数：{}", params);

        try {
            // 检查是否在季节性叫应时间段内
            if (!isInSeasonalPeriod()) {
                log.info("当前不在季节性叫应时间段内，跳过执行");
                return;
            }

            // 统一加载所有超时阈值配置
            loadTimeoutConfigs();

            // 获取叫应内容配置
            String[] checkConfig = getCheckContentConfig();

            // 根据配置检查相应内容
            if ("1".equals(checkConfig[0])) {
                checkPrePlanTimeout();
            }

            if ("1".equals(checkConfig[1])) {
                checkRescueTeamTimeout();
            }

            if ("1".equals(checkConfig[2])) {
                checkWarehouseTimeout();
            }

            if ("1".equals(checkConfig[3])) {
                checkExpertTimeout();
            }

            log.info("信息更新超时检查任务执行完成");
        } catch (Exception e) {
            log.error("信息更新超时检查任务执行失败", e);
            throw e;
        }
    }
    
    /**
     * 统一加载所有超时阈值配置
     */
    private void loadTimeoutConfigs() {
        try {
            // 直接调用配置服务获取当前配置
            InfoTimeoutTaskConfigDTO config = taskConfigService.getTaskConfig();

            // 将天数转换为分钟
            prePlanTimeoutMinutes = (config.getPrePlanTimeoutDays() != null ? config.getPrePlanTimeoutDays() : 30) * 24 * 60;
            rescueTeamTimeoutMinutes = (config.getRescueTeamTimeoutDays() != null ? config.getRescueTeamTimeoutDays() : 30) * 24 * 60;
            warehouseTimeoutMinutes = (config.getWarehouseTimeoutDays() != null ? config.getWarehouseTimeoutDays() : 30) * 24 * 60;
            expertTimeoutMinutes = (config.getExpertTimeoutDays() != null ? config.getExpertTimeoutDays() : 30) * 24 * 60;

            log.info("加载超时阈值配置 - 应急预案:{}天({}分钟), 救援队伍:{}天({}分钟), 物资仓库:{}天({}分钟), 专家信息:{}天({}分钟)",
                prePlanTimeoutMinutes / (24 * 60), prePlanTimeoutMinutes,
                rescueTeamTimeoutMinutes / (24 * 60), rescueTeamTimeoutMinutes,
                warehouseTimeoutMinutes / (24 * 60), warehouseTimeoutMinutes,
                expertTimeoutMinutes / (24 * 60), expertTimeoutMinutes);
        } catch (Exception e) {
            log.error("加载超时阈值配置失败，使用默认值", e);
            setDefaultTimeoutValues();
        }
    }

    /**
     * 设置默认超时阈值（将天数转换为分钟）
     */
    private void setDefaultTimeoutValues() {
        prePlanTimeoutMinutes = 30 * 24 * 60;    // 30天
        rescueTeamTimeoutMinutes = 30 * 24 * 60; // 30天
        warehouseTimeoutMinutes = 30 * 24 * 60;  // 30天
        expertTimeoutMinutes = 30 * 24 * 60;      // 30天
    }



    /**
     * 检查应急预案更新超时
     */
    private void checkPrePlanTimeout() {
        try {
            // 使用预加载的应急预案超时阈值
            int timeoutMinutes = prePlanTimeoutMinutes;

            // 计算超时时间点
            LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);

            // 查询超时的预案
            List<EmPrePlanVO> timeoutPlans = prePlanService.selectTimeoutPlans(timeoutTime);

            log.info("检查应急预案更新超时，超时阈值：{}分钟，发现超时预案：{}个", timeoutMinutes, timeoutPlans.size());

            // 为每个超时预案创建告警
            for (EmPrePlanVO plan : timeoutPlans) {
                // 检查是否已存在相同告警
                if (!alarmService.existsTimeoutAlarm("3", plan.getId())) {
                    createTimeoutAlarm("3", plan.getId(), plan.getPlanName(),
                                      plan.getLastCheckTime(), timeoutMinutes, plan.getCreator());
                }
            }
        } catch (Exception e) {
            log.error("检查应急预案更新超时失败", e);
        }
    }
    
    /**
     * 检查救援队伍更新超时
     */
    private void checkRescueTeamTimeout() {
        try {
            // 使用预加载的救援队伍超时阈值
            int timeoutMinutes = rescueTeamTimeoutMinutes;
            LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);

            List<RescueTeamVO> timeoutTeams = rescueTeamService.selectTimeoutTeams(timeoutTime);

            log.info("检查救援队伍更新超时，超时阈值：{}分钟，发现超时队伍：{}个", timeoutMinutes, timeoutTeams.size());

            for (RescueTeamVO team : timeoutTeams) {
                // 检查是否已存在相同告警
                if (!alarmService.existsTimeoutAlarm("7", team.getId())) {
                    createTimeoutAlarm("7", team.getId(), team.getTeamName(),
                                      team.getUpdateTime(), timeoutMinutes, team.getCreator());
                }
            }
        } catch (Exception e) {
            log.error("检查救援队伍更新超时失败", e);
        }
    }
    
    /**
     * 检查物资仓库更新超时
     */
    private void checkWarehouseTimeout() {
        try {
            // 使用预加载的物资仓库超时阈值
            int timeoutMinutes = warehouseTimeoutMinutes;
            LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(timeoutMinutes);

            List<WarehouseVO> timeoutWarehouses = warehouseService.selectTimeoutWarehouses(timeoutTime);

            log.info("检查物资仓库更新超时，超时阈值：{}分钟，发现超时仓库：{}个", timeoutMinutes, timeoutWarehouses.size());

            for (WarehouseVO warehouse : timeoutWarehouses) {
                // 检查是否已存在相同告警
                if (!alarmService.existsTimeoutAlarm("4", warehouse.getId())) {
                    createTimeoutAlarm("4", warehouse.getId(), warehouse.getWarehouseName(),
                                      warehouse.getUpdateTime(), timeoutMinutes, warehouse.getCreator());
                }
            }
        } catch (Exception e) {
            log.error("检查物资仓库更新超时失败", e);
        }
    }

    /**
     * 检查专家信息更新超时
     */
    private void checkExpertTimeout() {
        try {
            // 使用预加载的专家信息超时阈值
            int timeoutMinutes = expertTimeoutMinutes;

            // 计算超时时间点
            Date timeoutTime = DateUtils.addMinutes(new Date(), -timeoutMinutes);

            // 查询超时的专家信息
            List<ExpertInfoVO> timeoutExperts = expertService.selectTimeoutExperts(timeoutTime);

            log.info("检查专家信息更新超时，超时阈值：{}分钟，发现超时专家：{}个", timeoutMinutes, timeoutExperts.size());

            // 为每个超时专家创建告警
            for (ExpertInfoVO expert : timeoutExperts) {
                // 检查是否已存在相同告警
                if (!alarmService.existsTimeoutAlarm("5", expert.getId())) { // 5=应急通讯录
                    // 专家不是用户，使用专家专门的告警创建方法
                    createExpertTimeoutAlarm(expert, timeoutMinutes);

                    // 发送短信通知专家本人
                    sendSmsToExpert(expert, timeoutMinutes);
                }
            }

        } catch (Exception e) {
            log.error("检查专家信息更新超时失败", e);
        }
    }
    
    /**
     * 获取超时阈值（分钟）
     */
    private int getTimeoutMinutes(String dictLabel, int defaultValue) {
        try {
            String dictValue = dictDataService.selectDictLabel("info_update_timeout", dictLabel);
            if (StringUtils.isNotEmpty(dictValue)) {
                return Integer.parseInt(dictValue);
            }
        } catch (Exception e) {
            log.warn("获取字典值失败，使用默认值：{}", defaultValue, e);
        }
        return defaultValue;
    }
    
    /**
     * 创建超时告警
     */
    private void createTimeoutAlarm(String infoType, String infoId, String infoName,
                                   Date lastUpdateTime, int timeoutMinutes, String creatorName) {
        
        // 检查是否已经存在相同的告警（避免重复告警）
        if (alarmService.existsTimeoutAlarm(infoType, infoId)) {
            log.debug("{}[{}]已存在超时告警，跳过", infoType, infoName);
            return;
        }
        
        // 计算超时分钟数
        long overdueMinutes = ChronoUnit.MINUTES.between(
            lastUpdateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
            LocalDateTime.now()
        ) - timeoutMinutes;
        
        // 获取中文类型名称用于显示
        String infoTypeName = getInfoTypeName(infoType);

        // 构建告警标题
        String alarmTitle = String.format("%s信息更新超时", infoTypeName);

        // 构建告警内容
        String alarmContent = String.format(
            "%s\"%s\"已超过%d分钟未更新信息，当前已超时%d分钟。最后更新时间：%s。请及时更新相关信息以确保数据准确性。",
            infoTypeName, infoName, timeoutMinutes, overdueMinutes,
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, lastUpdateTime)
        );
        
        // 创建告警记录
        AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
        alarmInfo.setAlarmId(generateAlarmId()); // 生成唯一告警ID
        alarmInfo.setAlarmType("2"); // 信息更新超时对应字典值2
        alarmInfo.setAlarmSubtype(getAlarmSubtype(infoType)); // 设置告警子类型
        alarmInfo.setAlarmTitle(alarmTitle);
        alarmInfo.setAlarmContent(alarmContent);
        alarmInfo.setAlarmLevel("2"); // 中等级别
        alarmInfo.setSourceType(infoType); // 源数据类型
        alarmInfo.setSourceId(infoId); // 源数据ID

        // 获取创建人的部门信息
        String[] orgInfo = getCreatorOrgInfo(creatorName);
        alarmInfo.setOrgId(orgInfo[0]); // 设置组织ID
        alarmInfo.setOrgName(orgInfo[1]); // 设置组织名称
        alarmInfo.setAdministrativeAreaId("default"); // 设置默认行政区划ID
        alarmInfo.setAlarmTime(new Date());
        alarmInfo.setStatus("0"); // 未处理
        alarmInfo.setCreateBy("admin"); // 管理员创建
        alarmInfo.setCreateTime(new Date());

        // 直接调用mapper插入，避免使用SecurityUtils
        alarmInfoMapper.insertAlarmInfo(alarmInfo);
        
        log.info("创建{}更新超时告警：{}", infoTypeName, infoName);
    }

    /**
     * 根据信息类型获取告警子类型
     *
     * @param infoType 信息类型
     * @return 告警子类型
     */
    private String getAlarmSubtype(String infoType) {
        switch (infoType) {
            case "3": // 应急预案
                return "4"; // 预案更新超时
            case "4": // 应急物资
                return "5"; // 物资更新超时
            case "5": // 专家信息
                return "6"; // 专家更新超时
            case "7": // 应急救援队伍
                return "6"; // 通讯录更新超时
            default:
                return "4"; // 默认为预案更新超时
        }
    }

    /**
     * 根据信息类型字典值获取中文名称
     *
     * @param infoType 信息类型字典值
     * @return 中文名称
     */
    private String getInfoTypeName(String infoType) {
        switch (infoType) {
            case "3":
                return "应急预案";
            case "4":
                return "应急物资";
            case "5":
                return "应急通讯录";
            case "7":
                return "应急救援队伍";
            default:
                return "未知类型";
        }
    }


    /**
     * 创建专家超时告警
     *
     * @param expert 专家信息
     * @param timeoutMinutes 超时分钟数
     */
    private void createExpertTimeoutAlarm(ExpertInfoVO expert, int timeoutMinutes) {

        // 计算超时分钟数
        long overdueMinutes = ChronoUnit.MINUTES.between(
            expert.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
            LocalDateTime.now()
        );

        // 构建告警标题
        String alarmTitle = "应急通讯录信息更新超时";

        // 构建告警内容（使用原来的格式）
        String alarmContent = String.format(
            "应急通讯录\"%s\"已超过%d分钟未更新信息，当前已超时%d分钟。最后更新时间：%s。请及时更新相关信息以确保数据准确性。",
            expert.getName(), timeoutMinutes, overdueMinutes,
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, expert.getUpdateTime())
        );

        // 创建告警记录
        AlarmInfoDTO alarmInfo = new AlarmInfoDTO();
        alarmInfo.setAlarmId(generateAlarmId()); // 生成唯一告警ID
        alarmInfo.setAlarmType("2"); // 信息更新超时对应字典值2
        alarmInfo.setAlarmSubtype("6"); // 专家更新超时
        alarmInfo.setAlarmTitle(alarmTitle);
        alarmInfo.setAlarmContent(alarmContent);
        alarmInfo.setAlarmLevel("2"); // 中等级别
        alarmInfo.setSourceType("5"); // 应急通讯录
        alarmInfo.setSourceId(expert.getId()); // 专家ID

        // 获取专家所属部门信息
        String[] orgInfo = getExpertOrgInfo(expert);
        alarmInfo.setOrgId(orgInfo[0]); // 设置组织ID
        alarmInfo.setOrgName(orgInfo[1]); // 设置组织名称
        alarmInfo.setAdministrativeAreaId("default"); // 设置默认行政区划ID
        alarmInfo.setAlarmTime(new Date());
        alarmInfo.setStatus("0"); // 未处理
        alarmInfo.setCreateBy("admin"); // 管理员创建
        alarmInfo.setCreateTime(new Date());

        // 直接调用mapper插入，避免使用SecurityUtils
        alarmInfoMapper.insertAlarmInfo(alarmInfo);

        log.info("创建专家更新超时告警：{}", expert.getName());
    }

    /**
     * 获取专家所属组织信息
     *
     * @param expert 专家信息
     * @return 组织信息数组 [组织ID, 组织名称]
     */
    private String[] getExpertOrgInfo(ExpertInfoVO expert) {
        String[] defaultOrgInfo = {"default", "默认部门"};

        try {
            // 如果专家有部门ID，使用专家的部门信息
            if (expert.getDeptId() != null) {
                SysDept dept = deptService.selectDeptById(expert.getDeptId());
                if (dept != null) {
                    return new String[]{dept.getDeptId().toString(), dept.getDeptName()};
                }
            }

            // 如果专家有部门名称，直接使用
            if (StringUtils.isNotEmpty(expert.getDeptName())) {
                return new String[]{"default", expert.getDeptName()};
            }

            return defaultOrgInfo;

        } catch (Exception e) {
            log.error("获取专家[{}]的组织信息失败", expert.getName(), e);
            return defaultOrgInfo;
        }
    }

    /**
     * 根据创建人获取组织信息
     *
     * @param creatorName 创建人姓名
     * @return 组织信息数组 [组织ID, 组织名称]
     */
    private String[] getCreatorOrgInfo(String creatorName) {
        String[] defaultOrgInfo = {"default", "默认部门"};

        if (StringUtils.isEmpty(creatorName)) {
            return defaultOrgInfo;
        }

        try {
            // 根据创建人姓名查询用户信息
            SysUser user = userService.selectUserByUserName(creatorName);
            if (user == null || user.getDeptId() == null) {
                log.warn("未找到创建人[{}]的用户信息或部门信息", creatorName);
                return defaultOrgInfo;
            }

            // 根据部门ID查询部门信息
            SysDept dept = deptService.selectDeptById(user.getDeptId());
            if (dept == null) {
                log.warn("未找到部门信息，部门ID: {}", user.getDeptId());
                return defaultOrgInfo;
            }

            return new String[]{dept.getDeptId().toString(), dept.getDeptName()};

        } catch (Exception e) {
            log.error("获取创建人[{}]的组织信息失败", creatorName, e);
            return defaultOrgInfo;
        }
    }

    /**
     * 生成唯一的告警ID
     *
     * @return 告警ID
     */
    private String generateAlarmId() {
        return IdUtils.fastUUID();
    }

    /**
     * 发送短信通知专家
     *
     * @param expert 专家信息
     * @param timeoutMinutes 超时分钟数
     */
    private void sendSmsToExpert(ExpertInfoVO expert, int timeoutMinutes) {
        try {
            // 获取专家手机号
            String expertMobile = expert.getPhone();
            if (StringUtils.isEmpty(expertMobile)) {
                log.warn("专家手机号为空，无法发送短信通知，专家ID: {}", expert.getId());
                return;
            }

            // 构建短信内容
            String smsContent = buildExpertSmsContent(expert, timeoutMinutes);

            // 获取token并发送短信
            String token = getYkToken();
            if (token != null) {
                smsSend(token, expertMobile, smsContent);
                log.info("专家超时告警短信发送成功，手机号: {}, 内容: {}", expertMobile, smsContent);
            } else {
                log.error("获取短信token失败，无法发送专家超时告警短信");
            }
        } catch (Exception e) {
            // 短信发送失败不影响主业务流程，只记录日志
            log.error("发送专家超时告警短信失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建专家超时告警短信内容（JSON格式）
     *
     * @param expert 专家信息
     * @param timeoutMinutes 超时分钟数
     * @return 短信内容（JSON格式）
     */
    private String buildExpertSmsContent(ExpertInfoVO expert, int timeoutMinutes) {
        // 构建date字段（格式：2025年06月03日11时22分30秒）
        String dateStr = DateUtils.parseDateToStr("yyyy年MM月dd日HH时mm分ss秒", new Date());

        // 构建type字段
        String typeStr = "应急通讯录";

        // 构建content字段（参考告警内容格式）
        long overdueMinutes = ChronoUnit.MINUTES.between(
            expert.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
            LocalDateTime.now()
        );

        String contentStr = String.format(
            "专家%s已超过%d分钟未更新个人信息，当前已超时%d分钟。最后更新时间：%s。" +
            "工作单位：%s，专业领域：%s，联系电话：%s。",
            expert.getName(), timeoutMinutes, overdueMinutes,
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, expert.getUpdateTime()),
            expert.getWorkUnit() != null ? expert.getWorkUnit() : "未填写",
            expert.getSpecialtyField() != null ? expert.getSpecialtyField() : "未填写",
            expert.getPhone() != null ? expert.getPhone() : "未填写"
        );

        // 对JSON字符串中的特殊字符进行转义
        String escapedContent = escapeJsonString(contentStr);

        // 构建JSON格式的内容
        return String.format("{\"date\":\"%s\",\"type\":\"%s\",\"content\":\"%s\"}",
                dateStr, typeStr, escapedContent);
    }

    /**
     * 转义JSON字符串中的特殊字符
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    private String escapeJsonString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\\", "\\\\")  // 转义反斜杠
                  .replace("\"", "\\\"")  // 转义双引号
                  .replace("\b", "\\b")   // 转义退格符
                  .replace("\f", "\\f")   // 转义换页符
                  .replace("\n", "\\n")   // 转义换行符
                  .replace("\r", "\\r")   // 转义回车符
                  .replace("\t", "\\t");  // 转义制表符
    }

    /**
     * 获取云控平台Token
     *
     * @return Token字符串
     */
    private String getYkToken() {
        try {
            String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken";
            Map<String, String> body = new HashMap<>();
            body.put("appId", "fb9686ae-8706-46ce-926d-23ddfbc010e9");
            body.put("appSecret", "WTKkpkH*wAyFCKgbbfNz$sMnbuqd#Wj#");
            String response = HttpClientUtils.postWithBody(10000, url, null, body);
            YkTokenVO vo = new Gson().fromJson(response, YkTokenVO.class);
            if (vo.getCode() == 1) {
                return vo.getToken();
            }
            log.error("获取云控平台Token失败，响应: {}", response);
            return null;
        } catch (Exception e) {
            log.error("获取云控平台Token异常", e);
            return null;
        }
    }

    /**
     * 发送短信
     *
     * @param token Token
     * @param mobile 手机号
     * @param content 短信内容
     */
    private void smsSend(String token, String mobile, String content) {
        try {
            String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend";
            Map<String, String> head = new HashMap<>();
            head.put("AuthorizationType", "other");
            head.put("Authorization", token);
            Map<String, String> body = new HashMap<>();
            body.put("mobile", mobile);
            body.put("content", content);
            body.put("signName", "智慧高速云控平台");
            body.put("templateKey", "UPDATE_EMER_EVENT");
            String response = HttpClientUtils.postWithBody(10000, url, head, body);
            log.info("专家超时告警短信发送响应: {}", response);
        } catch (Exception e) {
            log.error("发送专家超时告警短信异常", e);
        }
    }

    /**
     * 检查是否在季节性叫应时间段内
     */
    private boolean isInSeasonalPeriod() {
        try {
            // 查找当前任务配置
            SysJob queryJob = new SysJob();
            queryJob.setJobName("应急信息更新超时检查");
            List<SysJob> jobList = jobService.selectJobList(queryJob);

            if (jobList.isEmpty()) {
                log.warn("未找到应急信息更新超时检查任务配置，默认执行");
                return true;
            }

            SysJob currentJob = jobList.get(0);
            String remark = currentJob.getRemark();

            // 如果备注中不包含季节性配置，则默认执行
            if (remark == null || !remark.contains("季节性叫应")) {
                return true;
            }

            // 解析季节性配置
            Calendar now = Calendar.getInstance();
            int currentMonth = now.get(Calendar.MONTH) + 1; // Calendar.MONTH 从0开始

            // 从备注中解析月份范围（简单实现，可以根据需要完善）
            if (remark.contains("3-9月")) {
                return currentMonth >= 3 && currentMonth <= 9;
            } else if (remark.contains("4-10月")) {
                return currentMonth >= 4 && currentMonth <= 10;
            } else if (remark.contains("5-11月")) {
                return currentMonth >= 5 && currentMonth <= 11;
            }

            // 默认3-9月
            return currentMonth >= 3 && currentMonth <= 9;

        } catch (Exception e) {
            log.error("检查季节性叫应时间段失败，默认执行", e);
            return true;
        }
    }

    /**
     * 获取叫应内容配置
     *
     * @return 配置数组 [预案, 救援队伍, 仓库, 专家]
     */
    private String[] getCheckContentConfig() {
        try {
            // 查找当前任务配置
            SysJob queryJob = new SysJob();
            queryJob.setJobName("应急信息更新超时检查");
            List<SysJob> jobList = jobService.selectJobList(queryJob);

            if (jobList.isEmpty()) {
                log.warn("未找到应急信息更新超时检查任务配置，默认检查所有内容");
                return new String[]{"1", "1", "1", "1"};
            }

            SysJob currentJob = jobList.get(0);
            String remark = currentJob.getRemark();

            if (remark == null) {
                return new String[]{"1", "1", "1", "1"};
            }

            // 从备注中解析叫应内容配置
            String checkPrePlan = remark.contains("检查预案") ? "1" : "0";
            String checkRescueTeam = remark.contains("检查救援队伍") ? "1" : "0";
            String checkWarehouse = remark.contains("检查仓库") ? "1" : "0";
            String checkExpert = remark.contains("检查专家") ? "1" : "0";

            // 如果没有任何配置信息，默认检查所有
            if ("0".equals(checkPrePlan) && "0".equals(checkRescueTeam) &&
                "0".equals(checkWarehouse) && "0".equals(checkExpert)) {
                return new String[]{"1", "1", "1", "1"};
            }

            return new String[]{checkPrePlan, checkRescueTeam, checkWarehouse, checkExpert};

        } catch (Exception e) {
            log.error("获取叫应内容配置失败，默认检查所有内容", e);
            return new String[]{"1", "1", "1", "1"};
        }
    }
}
