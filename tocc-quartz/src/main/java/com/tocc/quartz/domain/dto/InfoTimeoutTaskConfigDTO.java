package com.tocc.quartz.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 应急信息更新超时检查定时任务配置DTO
 * 
 * <AUTHOR>
 */
public class InfoTimeoutTaskConfigDTO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long jobId;

    /** 任务名称 */
    private String jobName;

    /** 任务状态（0正常 1暂停） */
    private String status;

    /** cron执行表达式 */
    private String cronExpression;

    /** 下次执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextValidTime;

    /** 叫应开始月份（1-12） */
    private Integer startMonth;

    /** 叫应结束月份（1-12） */
    private Integer endMonth;

    /** 叫应时间间隔（分钟） */
    private Integer intervalMinutes;

    /** 是否启用季节性叫应（0否 1是） */
    private String enableSeasonal;

    /** 是否检查应急预案（0否 1是） */
    private String checkPrePlan;

    /** 是否检查救援队伍（0否 1是） */
    private String checkRescueTeam;

    /** 是否检查物资仓库（0否 1是） */
    private String checkWarehouse;

    /** 是否检查专家信息（0否 1是） */
    private String checkExpert;

    /** 应急预案超时阈值（天） */
    private Integer prePlanTimeoutDays;

    /** 救援队伍超时阈值（天） */
    private Integer rescueTeamTimeoutDays;

    /** 物资仓库超时阈值（天） */
    private Integer warehouseTimeoutDays;

    /** 专家信息超时阈值（天） */
    private Integer expertTimeoutDays;

    /** 操作类型（runNow-立即执行一次） */
    private String operation;

    /** 备注 */
    private String remark;

    public Long getJobId()
    {
        return jobId;
    }

    public void setJobId(Long jobId)
    {
        this.jobId = jobId;
    }

    public String getJobName()
    {
        return jobName;
    }

    public void setJobName(String jobName)
    {
        this.jobName = jobName;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getCronExpression()
    {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression)
    {
        this.cronExpression = cronExpression;
    }

    public Date getNextValidTime()
    {
        return nextValidTime;
    }

    public void setNextValidTime(Date nextValidTime)
    {
        this.nextValidTime = nextValidTime;
    }

    public Integer getStartMonth()
    {
        return startMonth;
    }

    public void setStartMonth(Integer startMonth)
    {
        this.startMonth = startMonth;
    }

    public Integer getEndMonth()
    {
        return endMonth;
    }

    public void setEndMonth(Integer endMonth)
    {
        this.endMonth = endMonth;
    }

    public Integer getIntervalMinutes()
    {
        return intervalMinutes;
    }

    public void setIntervalMinutes(Integer intervalMinutes)
    {
        this.intervalMinutes = intervalMinutes;
    }

    public String getEnableSeasonal()
    {
        return enableSeasonal;
    }

    public void setEnableSeasonal(String enableSeasonal)
    {
        this.enableSeasonal = enableSeasonal;
    }

    public String getCheckPrePlan()
    {
        return checkPrePlan;
    }

    public void setCheckPrePlan(String checkPrePlan)
    {
        this.checkPrePlan = checkPrePlan;
    }

    public String getCheckRescueTeam()
    {
        return checkRescueTeam;
    }

    public void setCheckRescueTeam(String checkRescueTeam)
    {
        this.checkRescueTeam = checkRescueTeam;
    }

    public String getCheckWarehouse()
    {
        return checkWarehouse;
    }

    public void setCheckWarehouse(String checkWarehouse)
    {
        this.checkWarehouse = checkWarehouse;
    }

    public String getCheckExpert()
    {
        return checkExpert;
    }

    public void setCheckExpert(String checkExpert)
    {
        this.checkExpert = checkExpert;
    }

    public Integer getPrePlanTimeoutDays()
    {
        return prePlanTimeoutDays;
    }

    public void setPrePlanTimeoutDays(Integer prePlanTimeoutDays)
    {
        this.prePlanTimeoutDays = prePlanTimeoutDays;
    }

    public Integer getRescueTeamTimeoutDays()
    {
        return rescueTeamTimeoutDays;
    }

    public void setRescueTeamTimeoutDays(Integer rescueTeamTimeoutDays)
    {
        this.rescueTeamTimeoutDays = rescueTeamTimeoutDays;
    }

    public Integer getWarehouseTimeoutDays()
    {
        return warehouseTimeoutDays;
    }

    public void setWarehouseTimeoutDays(Integer warehouseTimeoutDays)
    {
        this.warehouseTimeoutDays = warehouseTimeoutDays;
    }

    public Integer getExpertTimeoutDays()
    {
        return expertTimeoutDays;
    }

    public void setExpertTimeoutDays(Integer expertTimeoutDays)
    {
        this.expertTimeoutDays = expertTimeoutDays;
    }

    public String getOperation()
    {
        return operation;
    }

    public void setOperation(String operation)
    {
        this.operation = operation;
    }

    @Override
    public String getRemark()
    {
        return remark;
    }

    @Override
    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    @Override
    public String toString()
    {
        return "InfoTimeoutTaskConfigDTO{" +
                "jobId=" + jobId +
                ", jobName='" + jobName + '\'' +
                ", status='" + status + '\'' +
                ", cronExpression='" + cronExpression + '\'' +
                ", nextValidTime=" + nextValidTime +
                ", startMonth=" + startMonth +
                ", endMonth=" + endMonth +
                ", intervalMinutes=" + intervalMinutes +
                ", enableSeasonal='" + enableSeasonal + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
