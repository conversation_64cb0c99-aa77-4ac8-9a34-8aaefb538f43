package com.tocc.quartz.service;

import com.tocc.common.core.domain.AjaxResult;
import com.tocc.quartz.domain.dto.InfoTimeoutTaskConfigDTO;
import org.quartz.SchedulerException;

/**
 * 应急信息更新超时检查定时任务配置服务接口
 * 
 * <AUTHOR>
 */
public interface IInfoTimeoutTaskConfigService
{
    /**
     * 获取应急信息更新超时检查任务配置
     * 
     * @return 任务配置信息
     */
    InfoTimeoutTaskConfigDTO getTaskConfig();

    /**
     * 更新任务时间间隔
     * 
     * @param intervalMinutes 时间间隔（分钟）
     * @return 操作结果
     */
    AjaxResult updateTaskInterval(Integer intervalMinutes) throws SchedulerException;

    /**
     * 设置季节性叫应时间段
     *
     * @param startMonth 开始月份（1-12）
     * @param endMonth 结束月份（1-12）
     * @param enableSeasonal 是否启用季节性叫应
     * @return 操作结果
     */
    AjaxResult setSeasonalPeriod(Integer startMonth, Integer endMonth, String enableSeasonal) throws SchedulerException;

    /**
     * 设置叫应内容配置
     *
     * @param checkPrePlan 是否检查应急预案
     * @param checkRescueTeam 是否检查救援队伍
     * @param checkWarehouse 是否检查物资仓库
     * @param checkExpert 是否检查专家信息
     * @return 操作结果
     */
    AjaxResult setCheckContent(String checkPrePlan, String checkRescueTeam, String checkWarehouse, String checkExpert) throws SchedulerException;

    /**
     * 设置超时阈值配置
     *
     * @param prePlanTimeoutDays 应急预案超时阈值（天）
     * @param rescueTeamTimeoutDays 救援队伍超时阈值（天）
     * @param warehouseTimeoutDays 物资仓库超时阈值（天）
     * @param expertTimeoutDays 专家信息超时阈值（天）
     * @return 操作结果
     */
    AjaxResult setTimeoutThresholds(Integer prePlanTimeoutDays, Integer rescueTeamTimeoutDays,
                                   Integer warehouseTimeoutDays, Integer expertTimeoutDays) throws SchedulerException;

    /**
     * 启动任务
     * 
     * @return 操作结果
     */
    AjaxResult startTask() throws SchedulerException;

    /**
     * 停止任务
     * 
     * @return 操作结果
     */
    AjaxResult stopTask() throws SchedulerException;

    /**
     * 立即执行一次任务
     * 
     * @return 操作结果
     */
    AjaxResult runTaskNow() throws SchedulerException;

    /**
     * 重置任务为默认配置
     * 
     * @return 操作结果
     */
    AjaxResult resetTaskConfig() throws SchedulerException;

    /**
     * 根据当前配置生成cron表达式
     * 
     * @param intervalMinutes 时间间隔（分钟）
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @param enableSeasonal 是否启用季节性
     * @return cron表达式
     */
    String generateCronExpression(Integer intervalMinutes, Integer startMonth, Integer endMonth, String enableSeasonal);
}
