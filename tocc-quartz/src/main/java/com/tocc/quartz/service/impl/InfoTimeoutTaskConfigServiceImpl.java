package com.tocc.quartz.service.impl;

import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.exception.job.TaskException;
import com.tocc.quartz.domain.SysJob;
import com.tocc.quartz.domain.dto.InfoTimeoutTaskConfigDTO;
import com.tocc.quartz.mapper.SysJobMapper;
import com.tocc.quartz.service.IInfoTimeoutTaskConfigService;
import com.tocc.quartz.service.ISysJobService;
import com.tocc.quartz.util.CronUtils;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import com.tocc.common.utils.DateUtils;

/**
 * 应急信息更新超时检查定时任务配置服务实现
 * 
 * <AUTHOR>
 */
@Service
public class InfoTimeoutTaskConfigServiceImpl implements IInfoTimeoutTaskConfigService
{
    private static final Logger log = LoggerFactory.getLogger(InfoTimeoutTaskConfigServiceImpl.class);

    /** 应急信息更新超时检查任务名称 */
    private static final String TASK_NAME = "应急信息更新超时检查";

    /** 默认时间间隔（分钟） */
    private static final Integer DEFAULT_INTERVAL = 10;

    /** 默认开始月份 */
    private static final Integer DEFAULT_START_MONTH = 3;

    /** 默认结束月份 */
    private static final Integer DEFAULT_END_MONTH = 9;

    @Autowired
    private ISysJobService jobService;

    @Autowired
    private SysJobMapper jobMapper;

    @Override
    public InfoTimeoutTaskConfigDTO getTaskConfig()
    {
        InfoTimeoutTaskConfigDTO config = new InfoTimeoutTaskConfigDTO();
        
        // 查找应急信息更新超时检查任务
        SysJob job = findInfoTimeoutTask();
        if (job != null)
        {
            config.setJobId(job.getJobId());
            config.setJobName(job.getJobName());
            config.setStatus(job.getStatus());
            config.setCronExpression(job.getCronExpression());
            config.setRemark(job.getRemark());
            
            // 计算下次执行时间
            if ("0".equals(job.getStatus()))
            {
                Date nextTime = CronUtils.getNextExecution(job.getCronExpression());
                config.setNextValidTime(nextTime);
            }
            
            // 解析当前配置
            parseCurrentConfig(config, job.getCronExpression(), job.getRemark());

            // 如果备注中没有配置信息，使用默认值
            if (config.getPrePlanTimeoutDays() == null) config.setPrePlanTimeoutDays(30);
            if (config.getRescueTeamTimeoutDays() == null) config.setRescueTeamTimeoutDays(30);
            if (config.getWarehouseTimeoutDays() == null) config.setWarehouseTimeoutDays(30);
            if (config.getExpertTimeoutDays() == null) config.setExpertTimeoutDays(30);
        }
        else
        {
            // 如果任务不存在，返回默认配置
            config.setJobName(TASK_NAME);
            config.setStatus("1"); // 暂停状态
            config.setIntervalMinutes(DEFAULT_INTERVAL);
            config.setStartMonth(DEFAULT_START_MONTH);
            config.setEndMonth(DEFAULT_END_MONTH);
            config.setEnableSeasonal("0");
            config.setCheckPrePlan("1");
            config.setCheckRescueTeam("1");
            config.setCheckWarehouse("1");
            config.setCheckExpert("1");
            // 设置默认超时阈值（天）
            config.setPrePlanTimeoutDays(30);
            config.setRescueTeamTimeoutDays(30);
            config.setWarehouseTimeoutDays(30);
            config.setExpertTimeoutDays(30);
            config.setCronExpression(generateCronExpression(DEFAULT_INTERVAL, null, null, "0"));
        }
        
        return config;
    }

    @Override
    public AjaxResult updateTaskInterval(Integer intervalMinutes) throws SchedulerException
    {
        if (intervalMinutes == null || intervalMinutes < 1 || intervalMinutes > 1440)
        {
            return AjaxResult.error("时间间隔必须在1-1440分钟之间");
        }

        SysJob job = findInfoTimeoutTask();
        if (job == null)
        {
            return AjaxResult.error("应急信息更新超时检查任务不存在");
        }

        // 解析当前配置
        InfoTimeoutTaskConfigDTO currentConfig = new InfoTimeoutTaskConfigDTO();
        parseCurrentConfig(currentConfig, job.getCronExpression(), job.getRemark());

        // 生成新的cron表达式
        String newCronExpression = generateCronExpression(intervalMinutes, 
            currentConfig.getStartMonth(), currentConfig.getEndMonth(), currentConfig.getEnableSeasonal());

        // 更新任务
        job.setCronExpression(newCronExpression);
        job.setRemark(String.format("应急信息更新超时检查任务，间隔：%d分钟 - 更新时间：%s",
            intervalMinutes, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date())));

        try
        {
            jobService.updateJob(job);
            log.info("应急信息更新超时检查任务时间间隔已更新为{}分钟", intervalMinutes);
            return AjaxResult.success("任务时间间隔更新成功");
        }
        catch (TaskException e)
        {
            log.error("更新任务时间间隔失败", e);
            return AjaxResult.error("更新任务时间间隔失败：" + e.getMessage());
        }
    }

    @Override
    public AjaxResult setSeasonalPeriod(Integer startMonth, Integer endMonth, String enableSeasonal) throws SchedulerException
    {
        if (startMonth != null && (startMonth < 1 || startMonth > 12))
        {
            return AjaxResult.error("开始月份必须在1-12之间");
        }
        if (endMonth != null && (endMonth < 1 || endMonth > 12))
        {
            return AjaxResult.error("结束月份必须在1-12之间");
        }

        SysJob job = findInfoTimeoutTask();
        if (job == null)
        {
            return AjaxResult.error("应急信息更新超时检查任务不存在");
        }

        // 解析当前配置
        InfoTimeoutTaskConfigDTO currentConfig = new InfoTimeoutTaskConfigDTO();
        parseCurrentConfig(currentConfig, job.getCronExpression(), job.getRemark());

        // 使用新值或保持原值
        Integer newStartMonth = startMonth != null ? startMonth : currentConfig.getStartMonth();
        Integer newEndMonth = endMonth != null ? endMonth : currentConfig.getEndMonth();
        String newEnableSeasonal = enableSeasonal != null ? enableSeasonal : currentConfig.getEnableSeasonal();

        // 生成新的cron表达式
        String newCronExpression = generateCronExpression(currentConfig.getIntervalMinutes(), 
            newStartMonth, newEndMonth, newEnableSeasonal);

        // 更新任务
        job.setCronExpression(newCronExpression);
        job.setRemark(String.format("应急信息更新超时检查任务，季节性叫应：%s - 更新时间：%s",
            "1".equals(newEnableSeasonal) ? String.format("%d-%d月", newStartMonth, newEndMonth) : "关闭",
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date())));

        try
        {
            jobService.updateJob(job);
            log.info("应急信息更新超时检查任务季节性配置已更新：{}-{}月，启用：{}", newStartMonth, newEndMonth, newEnableSeasonal);
            return AjaxResult.success("季节性配置更新成功");
        }
        catch (TaskException e)
        {
            log.error("更新季节性配置失败", e);
            return AjaxResult.error("更新季节性配置失败：" + e.getMessage());
        }
    }

    @Override
    public AjaxResult startTask() throws SchedulerException
    {
        SysJob job = findInfoTimeoutTask();
        if (job == null)
        {
            return AjaxResult.error("应急信息更新超时检查任务不存在");
        }

        if ("0".equals(job.getStatus()))
        {
            return AjaxResult.error("任务已经在运行中");
        }

        job.setStatus("0");
        int result = jobService.changeStatus(job);
        if (result > 0)
        {
            log.info("应急信息更新超时检查任务已启动");
            return AjaxResult.success("任务启动成功");
        }
        else
        {
            return AjaxResult.error("任务启动失败");
        }
    }

    @Override
    public AjaxResult stopTask() throws SchedulerException
    {
        SysJob job = findInfoTimeoutTask();
        if (job == null)
        {
            return AjaxResult.error("应急信息更新超时检查任务不存在");
        }

        if ("1".equals(job.getStatus()))
        {
            return AjaxResult.error("任务已经停止");
        }

        job.setStatus("1");
        int result = jobService.changeStatus(job);
        if (result > 0)
        {
            log.info("应急信息更新超时检查任务已停止");
            return AjaxResult.success("任务停止成功");
        }
        else
        {
            return AjaxResult.error("任务停止失败");
        }
    }

    @Override
    public AjaxResult runTaskNow() throws SchedulerException
    {
        SysJob job = findInfoTimeoutTask();
        if (job == null)
        {
            return AjaxResult.error("应急信息更新超时检查任务不存在");
        }

        boolean result = jobService.run(job);
        if (result)
        {
            log.info("应急信息更新超时检查任务已立即执行");
            return AjaxResult.success("任务已立即执行");
        }
        else
        {
            return AjaxResult.error("任务执行失败，任务可能不存在或已过期");
        }
    }

    @Override
    public AjaxResult setCheckContent(String checkPrePlan, String checkRescueTeam, String checkWarehouse, String checkExpert) throws SchedulerException
    {
        SysJob job = findInfoTimeoutTask();
        if (job == null)
        {
            return AjaxResult.error("应急信息更新超时检查任务不存在");
        }

        // 解析当前配置
        InfoTimeoutTaskConfigDTO currentConfig = new InfoTimeoutTaskConfigDTO();
        parseCurrentConfig(currentConfig, job.getCronExpression(), job.getRemark());

        // 使用新值或保持原值
        String newCheckPrePlan = checkPrePlan != null ? checkPrePlan : currentConfig.getCheckPrePlan();
        String newCheckRescueTeam = checkRescueTeam != null ? checkRescueTeam : currentConfig.getCheckRescueTeam();
        String newCheckWarehouse = checkWarehouse != null ? checkWarehouse : currentConfig.getCheckWarehouse();
        String newCheckExpert = checkExpert != null ? checkExpert : currentConfig.getCheckExpert();

        // 更新备注信息
        StringBuilder checkContent = new StringBuilder();
        if ("1".equals(newCheckPrePlan)) checkContent.append("预案,");
        if ("1".equals(newCheckRescueTeam)) checkContent.append("救援队伍,");
        if ("1".equals(newCheckWarehouse)) checkContent.append("仓库,");
        if ("1".equals(newCheckExpert)) checkContent.append("专家,");
        if (checkContent.length() > 0) checkContent.setLength(checkContent.length() - 1);

        job.setRemark(String.format("应急信息更新超时检查任务，检查内容：%s - 更新时间：%s",
            checkContent.toString(), DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date())));

        try
        {
            jobService.updateJob(job);
            log.info("应急信息更新超时检查任务叫应内容配置已更新：预案={}, 救援队伍={}, 仓库={}, 专家={}",
                newCheckPrePlan, newCheckRescueTeam, newCheckWarehouse, newCheckExpert);
            return AjaxResult.success("叫应内容配置更新成功");
        }
        catch (TaskException e)
        {
            log.error("更新叫应内容配置失败", e);
            return AjaxResult.error("更新叫应内容配置失败：" + e.getMessage());
        }
    }

    @Override
    public AjaxResult setTimeoutThresholds(Integer prePlanTimeoutDays, Integer rescueTeamTimeoutDays,
                                          Integer warehouseTimeoutDays, Integer expertTimeoutDays) throws SchedulerException
    {
        SysJob job = findInfoTimeoutTask();
        if (job == null)
        {
            return AjaxResult.error("应急信息更新超时检查任务不存在");
        }

        // 解析当前配置
        InfoTimeoutTaskConfigDTO currentConfig = new InfoTimeoutTaskConfigDTO();
        parseCurrentConfig(currentConfig, job.getCronExpression(), job.getRemark());

        // 使用新值或保持原值
        Integer newPrePlanTimeout = prePlanTimeoutDays != null ? prePlanTimeoutDays : currentConfig.getPrePlanTimeoutDays();
        Integer newRescueTeamTimeout = rescueTeamTimeoutDays != null ? rescueTeamTimeoutDays : currentConfig.getRescueTeamTimeoutDays();
        Integer newWarehouseTimeout = warehouseTimeoutDays != null ? warehouseTimeoutDays : currentConfig.getWarehouseTimeoutDays();
        Integer newExpertTimeout = expertTimeoutDays != null ? expertTimeoutDays : currentConfig.getExpertTimeoutDays();

        // 更新备注信息（简化版）
        job.setRemark(String.format("应急信息更新超时检查任务配置 - 更新时间：%s",
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date())));

        try
        {
            jobService.updateJob(job);
            log.info("应急信息更新超时检查任务超时阈值配置已更新：预案={}天, 救援队伍={}天, 仓库={}天, 专家={}天",
                newPrePlanTimeout, newRescueTeamTimeout, newWarehouseTimeout, newExpertTimeout);
            return AjaxResult.success("超时阈值配置更新成功");
        }
        catch (TaskException e)
        {
            log.error("更新超时阈值配置失败", e);
            return AjaxResult.error("更新超时阈值配置失败：" + e.getMessage());
        }
    }

    @Override
    public AjaxResult resetTaskConfig() throws SchedulerException
    {
        SysJob job = findInfoTimeoutTask();
        if (job == null)
        {
            return AjaxResult.error("应急信息更新超时检查任务不存在");
        }

        // 重置为默认配置
        String defaultCronExpression = generateCronExpression(DEFAULT_INTERVAL, null, null, "0");
        job.setCronExpression(defaultCronExpression);
        job.setRemark(String.format("应急信息更新超时检查任务 - 重置为默认配置，时间：%s",
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date())));

        try
        {
            jobService.updateJob(job);
            log.info("应急信息更新超时检查任务配置已重置为默认值");
            return AjaxResult.success("任务配置重置成功");
        }
        catch (TaskException e)
        {
            log.error("重置任务配置失败", e);
            return AjaxResult.error("重置任务配置失败：" + e.getMessage());
        }
    }

    @Override
    public String generateCronExpression(Integer intervalMinutes, Integer startMonth, Integer endMonth, String enableSeasonal)
    {
        if (intervalMinutes == null || intervalMinutes < 1)
        {
            intervalMinutes = DEFAULT_INTERVAL;
        }

        // 基础cron表达式：每N分钟执行一次
        String cronExpression = String.format("0 */%d * * * ?", intervalMinutes);

        // 如果启用季节性叫应，需要在任务执行时进行月份判断
        // 这里返回基础表达式，季节性判断在任务执行时进行
        return cronExpression;
    }

    /**
     * 查找应急信息更新超时检查任务
     */
    private SysJob findInfoTimeoutTask()
    {
        SysJob queryJob = new SysJob();
        queryJob.setJobName(TASK_NAME);
        List<SysJob> jobList = jobService.selectJobList(queryJob);
        return jobList.isEmpty() ? null : jobList.get(0);
    }

    /**
     * 解析当前配置
     */
    private void parseCurrentConfig(InfoTimeoutTaskConfigDTO config, String cronExpression, String remark)
    {
        // 从cron表达式解析时间间隔
        if (cronExpression != null && cronExpression.matches("0 \\*/\\d+ \\* \\* \\* \\?"))
        {
            String[] parts = cronExpression.split(" ");
            if (parts.length >= 2)
            {
                String minutePart = parts[1];
                if (minutePart.startsWith("*/"))
                {
                    try
                    {
                        config.setIntervalMinutes(Integer.parseInt(minutePart.substring(2)));
                    }
                    catch (NumberFormatException e)
                    {
                        config.setIntervalMinutes(DEFAULT_INTERVAL);
                    }
                }
            }
        }
        else
        {
            config.setIntervalMinutes(DEFAULT_INTERVAL);
        }

        // 设置默认值（不再从备注解析）
        config.setEnableSeasonal("0");
        config.setStartMonth(DEFAULT_START_MONTH);
        config.setEndMonth(DEFAULT_END_MONTH);
        config.setCheckPrePlan("1");
        config.setCheckRescueTeam("1");
        config.setCheckWarehouse("1");
        config.setCheckExpert("1");
        config.setPrePlanTimeoutDays(30);
        config.setRescueTeamTimeoutDays(30);
        config.setWarehouseTimeoutDays(30);
        config.setExpertTimeoutDays(30);
    }


}
