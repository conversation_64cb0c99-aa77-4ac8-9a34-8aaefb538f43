package com.tocc.web.riskController;


import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.risk.service.IRiskHomeService;
import com.tocc.risk.vo.RiskHome;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "风险一张图")
@RestController
@RequestMapping("/riskHome")
public class RiskHomeController extends BaseController {

    @Autowired
    private IRiskHomeService riskHomeService;


    @ApiOperation("获取点位详情")
    @GetMapping("/list")
    public AjaxResult getDropInfo(RiskHome home) {
        if (home.getType() == 1) {
            // 隐患点详情
            return success(riskHomeService.getPitfallsInfo(home));
        } else {
            // 项目点详情
            return success(riskHomeService.getProjectInfo(home));
        }
    }

    @ApiOperation("获取风险资源树")
    @GetMapping("/getResTree")
    public AjaxResult getResTree() {
        return success(riskHomeService.getResTree());
    }

    
}
