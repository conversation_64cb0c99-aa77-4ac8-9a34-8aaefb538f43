package com.tocc.web.riskController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.service.IPitfallsService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 隐患列Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags = "隐患列表")
@RestController
@RequestMapping("/risk/pitfalls")
public class PitfallsController extends BaseController
{
    @Autowired
    private IPitfallsService pitfallsService;

    /**
     * 查询隐患列列表
     */
    @ApiOperation("获取隐患列表")
    @PreAuthorize("@ss.hasPermi('risk:pitfalls:list')")
    @GetMapping("/list")
    public TableDataInfo list(Pitfalls pitfalls)
    {
        startPage();
        List<Pitfalls> list = pitfallsService.selectPitfallsList(pitfalls);
        return getDataTable(list);
    }

    /**
     * 导出隐患列列表
     */
    @ApiOperation("导出隐患列表")
    @PreAuthorize("@ss.hasPermi('risk:pitfalls:export')")
    @Log(title = "隐患列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Pitfalls pitfalls)
    {
        List<Pitfalls> list = pitfallsService.selectPitfallsList(pitfalls);
        ExcelUtil<Pitfalls> util = new ExcelUtil<Pitfalls>(Pitfalls.class);
        util.exportExcel(response, list, "隐患列数据");
    }

    /**
     * 获取隐患列详细信息
     */
    @ApiOperation("隐患详情")
    @PreAuthorize("@ss.hasPermi('risk:pitfalls:query')")
    @GetMapping(value = "/getInfo/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(pitfallsService.selectPitfallsById(id));
    }

    /**
     * 任务填报
     */
    @ApiOperation("任务填报")
    @PreAuthorize("@ss.hasPermi('riskManage:pitfalls:add')")
    @Log(title = "任务填报", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody Pitfalls pitfalls)
    {
        return toAjax(pitfallsService.insertPitfalls(pitfalls));
    }

    /**
     * 修改隐患列
     */
    @ApiOperation("修改隐患")
    @PreAuthorize("@ss.hasPermi('risk:pitfalls:edit')")
    @Log(title = "隐患列", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@RequestBody Pitfalls pitfalls)
    {
        return toAjax(pitfallsService.updatePitfalls(pitfalls));
    }

    /**
     * 删除隐患列
     */
    @ApiOperation("删除隐患")
    @PreAuthorize("@ss.hasPermi('risk:pitfalls:remove')")
    @Log(title = "隐患列", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody Pitfalls pitfalls)
    {
        return toAjax(pitfallsService.deletePitfallsById(pitfalls.getId()));
    }

    /**
     * 导出隐患分析报告
     */
    @ApiOperation("导出隐患分析报告")
    @PreAuthorize("@ss.hasPermi('risk:pitfalls:export')")
    @PostMapping("/exportAnalysis")
    public void exportAnalysis(HttpServletResponse response, Pitfalls pitfalls)
    {
        pitfallsService.exportAnalysis(response, pitfalls);
    }
}
