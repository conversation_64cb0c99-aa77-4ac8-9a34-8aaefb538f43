package com.tocc.web.controller.quartz;

import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.quartz.domain.dto.InfoTimeoutTaskConfigDTO;
import com.tocc.quartz.service.IInfoTimeoutTaskConfigService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 应急信息更新超时检查定时任务配置控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/infoTimeoutTask")
public class InfoTimeoutTaskConfigController extends BaseController
{
    @Autowired
    private IInfoTimeoutTaskConfigService taskConfigService;

    /**
     * 获取应急信息更新超时检查任务配置
     */
    @PreAuthorize("@ss.hasPermi('monitor:job:list')")
    @GetMapping("/config")
    public AjaxResult getTaskConfig()
    {
        InfoTimeoutTaskConfigDTO config = taskConfigService.getTaskConfig();
        return success(config);
    }

    /**
     * 统一配置接口 - 支持所有配置项的设置
     */
    @PreAuthorize("@ss.hasPermi('monitor:job:edit')")
    @Log(title = "应急信息超时检查任务", businessType = BusinessType.UPDATE)
    @PostMapping("/config")
    public AjaxResult updateConfig(@RequestBody InfoTimeoutTaskConfigDTO config)
    {
        try
        {
            AjaxResult result;

            // 1. 更新时间间隔
            if (config.getIntervalMinutes() != null)
            {
                result = taskConfigService.updateTaskInterval(config.getIntervalMinutes());
                if (!result.isSuccess())
                {
                    return result;
                }
            }

            // 2. 更新季节性配置
            if (config.getStartMonth() != null || config.getEndMonth() != null || config.getEnableSeasonal() != null)
            {
                result = taskConfigService.setSeasonalPeriod(config.getStartMonth(), config.getEndMonth(), config.getEnableSeasonal());
                if (!result.isSuccess())
                {
                    return result;
                }
            }

            // 2.5. 更新叫应内容配置
            if (config.getCheckPrePlan() != null || config.getCheckRescueTeam() != null ||
                config.getCheckWarehouse() != null || config.getCheckExpert() != null)
            {
                result = taskConfigService.setCheckContent(config.getCheckPrePlan(), config.getCheckRescueTeam(),
                    config.getCheckWarehouse(), config.getCheckExpert());
                if (!result.isSuccess())
                {
                    return result;
                }
            }

            // 2.6. 更新超时阈值配置
            if (config.getPrePlanTimeoutDays() != null || config.getRescueTeamTimeoutDays() != null ||
                config.getWarehouseTimeoutDays() != null || config.getExpertTimeoutDays() != null)
            {
                result = taskConfigService.setTimeoutThresholds(config.getPrePlanTimeoutDays(),
                    config.getRescueTeamTimeoutDays(), config.getWarehouseTimeoutDays(), config.getExpertTimeoutDays());
                if (!result.isSuccess())
                {
                    return result;
                }
            }

            // 3. 立即执行一次（如果请求）
            if ("runNow".equals(config.getOperation()))
            {
                result = taskConfigService.runTaskNow();
                if (!result.isSuccess())
                {
                    return result;
                }
            }

            // 4. 更新任务状态（启动/停止）
            if (config.getStatus() != null)
            {
                if ("0".equals(config.getStatus()))
                {
                    result = taskConfigService.startTask();
                }
                else
                {
                    result = taskConfigService.stopTask();
                }
                if (!result.isSuccess())
                {
                    return result;
                }
            }

            return success("任务配置更新成功");
        }
        catch (SchedulerException e)
        {
            logger.error("配置任务失败", e);
            return error("配置任务失败：" + e.getMessage());
        }
    }

    /**
     * 重置任务配置为默认值
     */
    @PreAuthorize("@ss.hasPermi('monitor:job:edit')")
    @Log(title = "应急信息超时检查任务", businessType = BusinessType.UPDATE)
    @PostMapping("/reset")
    public AjaxResult resetTaskConfig()
    {
        try
        {
            return taskConfigService.resetTaskConfig();
        }
        catch (SchedulerException e)
        {
            logger.error("重置任务配置失败", e);
            return error("重置任务配置失败：" + e.getMessage());
        }
    }
}
