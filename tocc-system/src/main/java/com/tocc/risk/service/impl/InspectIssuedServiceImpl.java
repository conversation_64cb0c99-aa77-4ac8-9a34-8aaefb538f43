package com.tocc.risk.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.risk.domain.InspectTask;
import com.tocc.risk.domain.Projects;
import com.tocc.risk.mapper.InspectTaskMapper;
import com.tocc.risk.mapper.RiskProjectsMapper;
import com.tocc.risk.service.IInspectTaskService;
import com.tocc.system.mapper.SysDeptMapper;
import com.tocc.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.InspectIssuedMapper;
import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.service.IInspectIssuedService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 检查下发Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class InspectIssuedServiceImpl implements IInspectIssuedService 
{
    @Autowired
    private InspectIssuedMapper inspectIssuedMapper;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private InspectTaskMapper inspectTaskMapper;
    @Autowired
    private RiskProjectsMapper projectsMapper;
    @Autowired
    private SysUserMapper userMapper;


    /**
     * 查询检查下发
     * 
     * @param id 检查下发主键
     * @return 检查下发
     */
    @Override
    public InspectIssued selectInspectIssuedById(Long id)
    {
        return inspectIssuedMapper.selectInspectIssuedById(id);
    }

    /**
     * 查询检查下发列表
     * 
     * @param inspectIssued 检查下发
     * @return 检查下发
     */
    @Override
    public List<InspectIssued> selectInspectIssuedList(InspectIssued inspectIssued)
    {
        return inspectIssuedMapper.selectInspectIssuedList(inspectIssued);
    }

    @Override
    public List<InspectTask> getProgress(InspectIssued inspectIssued) {
        return inspectIssuedMapper.getProgress(inspectIssued.getId());
    }

    /**
     * 新增检查下发
     * 
     * @param inspectIssued 检查下发
     * @return 结果
     */
    @Override
    @Transactional
    public int insertInspectIssued(InspectIssued inspectIssued)
    {
        // 切割填报人ID，获取下发单位，
        String[] str = getUniutNameAndId(inspectIssued.getTaskList());
        // 项目填报
        String[] pro = getProNameAndId(inspectIssued.getTaskList());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysDept de = deptMapper.selectDeptById(loginUser.getDeptId());
        inspectIssued.setIssuedUnit(de.getDeptName());
        inspectIssued.setIssuedUnitId(de.getDeptId());
        inspectIssued.setStatus(1);
        inspectIssued.setDelFlag(0);
        inspectIssued.setUnits(str[0]);
        inspectIssued.setUnitId(str[1]);
        inspectIssued.setProject(pro[0]);
        inspectIssued.setProjectId(pro[1]);
        inspectIssued.setCreateTime(DateUtils.getNowDate());
        inspectIssuedMapper.insertInspectIssued(inspectIssued);

        return insertTask(inspectIssued.getTaskList(), inspectIssued.getId());
    }

    /**
     * 获取单位名称拼接数据
     * @param tasks
     * @return
     */
    private String[] getUniutNameAndId(List<InspectTask> tasks) {
        String deptName = "";
        String ids = "";
        for (int i = 0; i < tasks.size(); i++) {
            InspectTask task = tasks.get(i);
            String[] fillIds = {};
            if (StringUtils.isNotEmpty(task.getFillIds())) {
                fillIds = task.getFillIds().split(",");
            } else {
                // 没有就循环下一个
                continue;
            }
            String deptIds = "";
            for (int i1 = 0; i1 < fillIds.length; i1++) {
                deptIds = deptMapper.getAncestorsByUserId(fillIds[i1]);
                if (StringUtils.isEmpty(deptIds)) {
                    // 没有就循环下一个
                    continue;
                }
                String[] deptId = deptIds.split(",");
                SysDept dept = null;
                if (deptId.length >= 3) {
                    dept = deptMapper.selectDeptById(Long.parseLong(deptId[2]));
                }else {
                    if (deptId[deptId.length - 1].equals("0")) {
                        // 最高级部门
                        dept = deptMapper.selectDeptById(100L);
                    }else {
                        dept = deptMapper.selectDeptById(Long.parseLong(deptId[deptId.length - 1]));
                    }
                }
                deptName = deptName + dept.getDeptName() + ",";
                ids = ids + dept.getDeptId() + ",";
            }
        }
        if (StringUtils.isEmpty(ids)) {
            return new String[2];
        }
        String[] str = {deptName.substring(0, deptName.length()-1), ids.substring(0, ids.length()-1)};
        return str;
    }

    /**
     * 获取项目拼接数据
     * @param tasks
     * @return
     */
    private String[] getProNameAndId(List<InspectTask> tasks) {
        String name = "";
        String ids = "";
        for (int i = 0; i < tasks.size(); i++) {
            InspectTask task = tasks.get(i);
            String[] proIds = {};
            if (StringUtils.isNotEmpty(task.getProjectId())) {
                proIds = task.getProjectId().split(",");
            } else {
                // 没有就循环下一个
                continue;
            }
            for (int i1 = 0; i1 < proIds.length; i1++) {
                Projects projects = projectsMapper.selectRiskProjectsById(Long.parseLong(proIds[i1]));
                if (projects == null || projects.getId() == null) {
                    continue;
                }
                name += projects.getProjectName() + ",";
                ids += projects.getId() + ",";
            }
        }
        String[] str = {name.substring(0, name.length()-1), ids.substring(0, ids.length()-1)};
        return str;
    }

    /**
     * 执行下发检查任务
     * @return
     */
    private int insertTask(List<InspectTask> tasks, Long issuedId) {
         int i = 0;
        // 填入检查任务表
        for (InspectTask task : tasks) {
            String[] ids = task.getBuilderIds().split(",");
            if (ids.length > 0) {
                // 项目填报人下发检查任务
                for (String id : ids) {
                    SysUser user = userMapper.selectUserById(Long.parseLong(id));
                    InspectTask ta = new InspectTask();
                    ta.setArea(task.getArea());
                    ta.setProjectId(ta.getProjectId());
                    ta.setIssuedId(issuedId);
                    ta.setInformantId(user.getUserId());
                    ta.setInformant(user.getNickName());
                    ta.setStatus(0);
                    ta.setDelFlag(0);
                    ta.setCreateTime(DateUtils.getNowDate());
                    inspectTaskMapper.insertInspectTask(ta);
                    i++;
                }
            } else {
                // 单位填报人下发检查任务
                ids = task.getFillIds().split(",");
                for (String id : ids) {
                    SysUser user = userMapper.selectUserById(Long.parseLong(id));
                    InspectTask ta = new InspectTask();
                    ta.setArea(task.getArea());
                    ta.setIssuedId(issuedId);
                    ta.setInformantId(user.getUserId());
                    ta.setInformant(user.getNickName());
                    ta.setStatus(0);
                    ta.setDelFlag(0);
                    ta.setCreateTime(DateUtils.getNowDate());
                    inspectTaskMapper.insertInspectTask(ta);
                    i++;
                }
            }
        }
        return i;
    }

    /**
     * 修改检查下发
     * 
     * @param inspectIssued 检查下发
     * @return 结果
     */
    @Override
    public int updateInspectIssued(InspectIssued inspectIssued)
    {
        inspectIssued.setUpdateTime(DateUtils.getNowDate());
        return inspectIssuedMapper.updateInspectIssued(inspectIssued);
    }

    /**
     * 批量删除检查下发
     * 
     * @param ids 需要删除的检查下发主键
     * @return 结果
     */
    @Override
    public int deleteInspectIssuedByIds(Long[] ids)
    {
        return inspectIssuedMapper.deleteInspectIssuedByIds(ids);
    }

    /**
     * 删除检查下发信息
     * 
     * @param id 检查下发主键
     * @return 结果
     */
    @Override
    public int deleteInspectIssuedById(Long id)
    {
        return inspectIssuedMapper.deleteInspectIssuedById(id);
    }
}
