package com.tocc.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tocc.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 专家信息VO
 *
 * <AUTHOR>
 */
@Data
public class ExpertInfoVO {

    /**
     * 主键
     */
    @Excel(name = "主键")
    private String id;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别
     * （0男 1女 2未知）
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 部门Id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 从事专业（擅长专业）
     */
    @Excel(name = "从事专业（擅长专业）")
    private String specialtyField;

    /**
     * 职称
     */
    @Excel(name = "职称")
    private String professionalTitle;
    /**
     * 职务
     */
    @Excel(name = "职务")
    private String post;

    /**
     * 工作单位
     */
    @Excel(name = "工作单位")
    private String workUnit;

    /**
     * 学历
     */
    @Excel(name = "学历")
    private String education;

    /**
     * 学历专业
     */
    private String major;

    /**
     * 毕业学校
     */
    private String graduationSchool;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String phone;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;


    /**
     * 联系地址
     */
    @Excel(name = "联系地址")
    private String address;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 申报类型
     */
    @Excel(name = "申报类型")
    private String declarationType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    @JsonIgnore
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @JsonIgnore
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
