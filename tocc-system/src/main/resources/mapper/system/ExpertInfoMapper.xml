<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.system.mapper.ExpertInfoMapper">
    <resultMap type="com.tocc.system.domain.vo.ExpertInfoVO" id="ExpertInfoVOResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="deptId" column="dept_id"/>
        <result property="specialtyField" column="specialty_field"/>
        <result property="professionalTitle" column="professional_title"/>
        <result property="workUnit" column="work_unit"/>
        <result property="education" column="education"/>
        <result property="major" column="major"/>
        <result property="graduationSchool" column="graduation_School"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="address" column="address"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="declarationType" column="declaration_type"/>
        <result property="deptName" column="dept_name"/>
    </resultMap>

    <sql id="selectExpertInfoVo">
        select id,
               name,
               sex,
               birthday,
               dept_id,
               specialty_field,
               professional_title,
               work_unit,
               education,
               major,
               graduation_school,
               phone,
               email,
               address,
               longitude,
               latitude,
               declaration_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from expert_info
    </sql>

    <select id="selectExpertInfoById" parameterType="java.lang.String" resultMap="ExpertInfoVOResult">
        <include refid="selectExpertInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectExpertInfoList" parameterType="com.tocc.system.domain.dto.ExpertInfoDTO"
            resultMap="ExpertInfoVOResult">
        select * from expert_info e
        LEFT JOIN sys_dept d ON e.dept_id = d.dept_id
        <where>
            <if test="id != null">e.id = #{id}</if>
            <if test="name != null and name != ''">AND e.name LIKE CONCAT('%',
                #{name}, '%')
            </if>
            <if test="specialtyField != null and specialtyField != ''">AND e.specialty_field LIKE CONCAT('%',
                #{specialtyField}, '%')
            </if>
            <if test="professionalTitle != null and professionalTitle != ''">AND e.professional_title LIKE CONCAT('%',
                #{professionalTitle}, '%')
            </if>
            <if test="workUnit != null and workUnit != ''">AND e.work_unit LIKE CONCAT('%', #{workUnit}, '%')</if>
        </where>

        ORDER BY e.update_time DESC
    </select>

    <select id="selectTimeoutExperts" parameterType="Date" resultMap="ExpertInfoVOResult">
        SELECT e.id,
               e.specialty_field,
               e.professional_title,
               e.work_unit,
               e.phone,
               e.email,
               e.update_time,
               e.create_by,
               e.name,
               d.dept_id,
               d.dept_name
        FROM expert_info e
                 LEFT JOIN sys_dept d ON e.dept_id = d.dept_id
        WHERE e.update_time &lt; #{timeoutTime}
        ORDER BY e.update_time ASC
    </select>

    <insert id="insertExpertInfo" parameterType="com.tocc.system.domain.dto.ExpertInfoDTO">
        INSERT INTO expert_info (id, name, sex, birthday, dept_id, specialty_field, professional_title, work_unit,
                                 education,
                                 major, graduation_school, phone, email, address, longitude, latitude, declaration_type,
                                 create_by, create_time, update_by, update_time, remark)
        VALUES (#{id}, #{name}, #{sex}, #{birthday}, #{deptId}, #{specialtyField}, #{professionalTitle}, #{workUnit},
                #{education},
                #{major}, #{graduationSchool}, #{phone}, #{email}, #{address}, #{longitude}, #{latitude},
                #{declarationType}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark})
    </insert>

    <update id="updateExpertInfo" parameterType="com.tocc.system.domain.dto.ExpertInfoDTO">
        UPDATE expert_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name !=''">name = #{name},</if>
            <if test="sex != null and sex !=''">sex = #{sex},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="specialtyField != null and specialtyField !=''">specialty_field = #{specialtyField},</if>
            <if test="professionalTitle != null and professionalTitle !=''">professional_title = #{professionalTitle},</if>
            <if test="workUnit != null and workUnit !=''">work_unit = #{workUnit},</if>
            <if test="education != null and education !=''">education = #{education},</if>
            <if test="major != null and major !=''">major = #{major},</if>
            <if test="graduationSchool != null and graduationSchool !=''">graduation_school = #{graduationSchool},</if>
            <if test="phone != null and phone !=''">phone = #{phone},</if>
            <if test="email != null and email !=''">email = #{email},</if>
            <if test="address != null and address !=''">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="declarationType != null and declarationType !=''">declaration_type = #{declarationType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteExpertInfoById" parameterType="Long">
        DELETE
        FROM expert_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteExpertInfoByIds" parameterType="java.util.List">
        DELETE FROM expert_info WHERE id IN
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="countById" resultType="java.lang.Integer">
        select count(id)
        from expert_info
        where id = #{id}
    </select>

</mapper>
