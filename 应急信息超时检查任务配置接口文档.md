# 应急信息更新超时检查定时任务配置接口文档

## 概述

本接口用于配置应急信息更新超时检查定时任务，支持时间间隔设置、季节性叫应配置、检查内容选择、超时阈值配置等功能。

## 配置架构

### 数据流设计
```
前端表单 → DTO字段 → 服务层 → 定时任务
    ↓         ↓        ↓        ↓
  用户输入 → 直接传递 → 直接存储 → 直接获取
```

### 配置存储方式
- **主要配置**：通过DTO字段直接传递和存储
- **备注字段**：仅用于记录操作时间，不存储业务配置
- **配置获取**：定时任务直接调用配置服务获取最新配置

### 优势特点
1. **简洁直观**：配置直接通过字段传递，无需解析
2. **实时生效**：配置更新后立即生效
3. **易于维护**：新增配置只需添加DTO字段
4. **错误率低**：避免字符串解析可能的错误

## 接口列表

### 1. 获取任务配置

**接口地址：** `GET /monitor/infoTimeoutTask/config`

**请求参数：** 无

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "jobId": 100,
    "jobName": "应急信息更新超时检查",
    "status": "0",
    "cronExpression": "0 */30 * * * ?",
    "nextValidTime": "2025-01-13 16:30:00",
    "intervalMinutes": 30,
    "startMonth": 3,
    "endMonth": 9,
    "enableSeasonal": "1",
    "checkPrePlan": "1",
    "checkRescueTeam": "1",
    "checkWarehouse": "0",
    "checkExpert": "1",
    "prePlanTimeoutDays": 30,
    "rescueTeamTimeoutDays": 30,
    "warehouseTimeoutDays": 30,
    "expertTimeoutDays": 30,
    "remark": "应急信息更新超时检查任务配置 - 更新时间：2025-01-13 16:30:00"
  }
}
```

### 2. 更新任务配置

**接口地址：** `POST /monitor/infoTimeoutTask/config`

**请求参数：**
```json
{
  "intervalMinutes": 60,        // 执行间隔（分钟，1-1440）
  "startMonth": 4,              // 季节性开始月份（1-12）
  "endMonth": 10,               // 季节性结束月份（1-12）
  "enableSeasonal": "1",        // 是否启用季节性叫应（0否 1是）
  "checkPrePlan": "1",          // 是否检查应急预案（0否 1是）
  "checkRescueTeam": "1",       // 是否检查救援队伍（0否 1是）
  "checkWarehouse": "0",        // 是否检查物资仓库（0否 1是）
  "checkExpert": "1",           // 是否检查专家信息（0否 1是）
  "prePlanTimeoutDays": 7,      // 应急预案超时阈值（天）
  "rescueTeamTimeoutDays": 30,  // 救援队伍超时阈值（天）
  "warehouseTimeoutDays": 30,   // 物资仓库超时阈值（天）
  "expertTimeoutDays": 7,       // 专家信息超时阈值（天）
  "status": "0",                // 任务状态（0启动 1停止）
  "operation": "runNow"         // 操作类型（runNow-立即执行一次）
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "任务配置更新成功"
}
```

### 3. 重置任务配置

**接口地址：** `POST /monitor/infoTimeoutTask/reset`

**请求参数：** 无

**响应示例：**
```json
{
  "code": 200,
  "msg": "任务配置重置成功"
}
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| intervalMinutes | Integer | 否 | 执行间隔（分钟），范围：1-1440 |
| startMonth | Integer | 否 | 季节性叫应开始月份，范围：1-12 |
| endMonth | Integer | 否 | 季节性叫应结束月份，范围：1-12 |
| enableSeasonal | String | 否 | 是否启用季节性叫应，0=否，1=是 |
| checkPrePlan | String | 否 | 是否检查应急预案，0=否，1=是 |
| checkRescueTeam | String | 否 | 是否检查救援队伍，0=否，1=是 |
| checkWarehouse | String | 否 | 是否检查物资仓库，0=否，1=是 |
| checkExpert | String | 否 | 是否检查专家信息，0=否，1=是 |
| prePlanTimeoutDays | Integer | 否 | 应急预案超时阈值（天），范围：1-365 |
| rescueTeamTimeoutDays | Integer | 否 | 救援队伍超时阈值（天），范围：1-365 |
| warehouseTimeoutDays | Integer | 否 | 物资仓库超时阈值（天），范围：1-365 |
| expertTimeoutDays | Integer | 否 | 专家信息超时阈值（天），范围：1-365 |
| status | String | 否 | 任务状态，0=启动，1=停止 |
| operation | String | 否 | 操作类型，runNow=立即执行一次 |

## Vue3 代码示例

### 1. API 服务封装

```javascript
// api/monitor/infoTimeoutTask.js
import request from '@/utils/request'

// 获取任务配置
export function getTaskConfig() {
  return request({
    url: '/monitor/infoTimeoutTask/config',
    method: 'get'
  })
}

// 更新任务配置
export function updateTaskConfig(data) {
  return request({
    url: '/monitor/infoTimeoutTask/config',
    method: 'post',
    data: data
  })
}

// 重置任务配置
export function resetTaskConfig() {
  return request({
    url: '/monitor/infoTimeoutTask/reset',
    method: 'post'
  })
}
```

### 2. Vue3 组件示例

```vue
<template>
  <div class="info-timeout-task-config">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>应急信息更新超时检查任务配置</span>
        </div>
      </template>
      
      <el-form ref="configFormRef" :model="configForm" :rules="rules" label-width="120px">
        <!-- 执行间隔 -->
        <el-form-item label="执行间隔" prop="intervalMinutes">
          <el-input-number 
            v-model="configForm.intervalMinutes" 
            :min="1" 
            :max="1440" 
            controls-position="right"
            style="width: 200px"
          />
          <span class="ml-2 text-gray-500">分钟</span>
        </el-form-item>

        <!-- 季节性叫应 -->
        <el-form-item label="季节性叫应">
          <el-switch 
            v-model="seasonalEnabled" 
            @change="handleSeasonalChange"
          />
        </el-form-item>

        <!-- 季节性时间段 -->
        <el-form-item 
          v-if="seasonalEnabled" 
          label="叫应时间段" 
          prop="seasonalPeriod"
        >
          <el-select v-model="configForm.startMonth" placeholder="开始月份" style="width: 100px">
            <el-option 
              v-for="month in months" 
              :key="month.value" 
              :label="month.label" 
              :value="month.value"
            />
          </el-select>
          <span class="mx-2">至</span>
          <el-select v-model="configForm.endMonth" placeholder="结束月份" style="width: 100px">
            <el-option 
              v-for="month in months" 
              :key="month.value" 
              :label="month.label" 
              :value="month.value"
            />
          </el-select>
        </el-form-item>

        <!-- 叫应内容 -->
        <el-form-item label="叫应内容">
          <el-checkbox-group v-model="checkContent">
            <el-checkbox label="checkPrePlan">应急预案</el-checkbox>
            <el-checkbox label="checkRescueTeam">救援队伍</el-checkbox>
            <el-checkbox label="checkWarehouse">物资仓库</el-checkbox>
            <el-checkbox label="checkExpert">专家信息</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 超时阈值配置 -->
        <el-form-item label="超时阈值配置">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-form-item label="应急预案" label-width="80px">
                <el-input-number
                  v-model="configForm.prePlanTimeoutDays"
                  :min="1"
                  :max="365"
                  controls-position="right"
                  style="width: 100px"
                />
                <span class="ml-1 text-gray-500">天</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="救援队伍" label-width="80px">
                <el-input-number
                  v-model="configForm.rescueTeamTimeoutDays"
                  :min="1"
                  :max="365"
                  controls-position="right"
                  style="width: 100px"
                />
                <span class="ml-1 text-gray-500">天</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="物资仓库" label-width="80px">
                <el-input-number
                  v-model="configForm.warehouseTimeoutDays"
                  :min="1"
                  :max="365"
                  controls-position="right"
                  style="width: 100px"
                />
                <span class="ml-1 text-gray-500">天</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="专家信息" label-width="80px">
                <el-input-number
                  v-model="configForm.expertTimeoutDays"
                  :min="1"
                  :max="365"
                  controls-position="right"
                  style="width: 100px"
                />
                <span class="ml-1 text-gray-500">天</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <!-- 任务状态 -->
        <el-form-item label="任务状态">
          <el-radio-group v-model="configForm.status">
            <el-radio label="0">启动</el-radio>
            <el-radio label="1">停止</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 下次执行时间 -->
        <el-form-item v-if="configForm.nextValidTime" label="下次执行时间">
          <span class="text-blue-600">{{ configForm.nextValidTime }}</span>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSave" :loading="loading">
            保存配置
          </el-button>
          <el-button @click="handleRunNow" :loading="runNowLoading">
            立即执行一次
          </el-button>
          <el-button @click="handleReset">
            重置为默认
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTaskConfig, updateTaskConfig, resetTaskConfig } from '@/api/monitor/infoTimeoutTask'

// 响应式数据
const configFormRef = ref()
const loading = ref(false)
const runNowLoading = ref(false)

const configForm = reactive({
  intervalMinutes: 10,
  startMonth: 3,
  endMonth: 9,
  enableSeasonal: '0',
  checkPrePlan: '1',
  checkRescueTeam: '1',
  checkWarehouse: '1',
  checkExpert: '1',
  prePlanTimeoutDays: 30,
  rescueTeamTimeoutDays: 30,
  warehouseTimeoutDays: 30,
  expertTimeoutDays: 30,
  status: '1',
  nextValidTime: null
})

// 季节性叫应开关
const seasonalEnabled = computed({
  get: () => configForm.enableSeasonal === '1',
  set: (value) => {
    configForm.enableSeasonal = value ? '1' : '0'
  }
})

// 叫应内容复选框
const checkContent = computed({
  get: () => {
    const content = []
    if (configForm.checkPrePlan === '1') content.push('checkPrePlan')
    if (configForm.checkRescueTeam === '1') content.push('checkRescueTeam')
    if (configForm.checkWarehouse === '1') content.push('checkWarehouse')
    if (configForm.checkExpert === '1') content.push('checkExpert')
    return content
  },
  set: (value) => {
    configForm.checkPrePlan = value.includes('checkPrePlan') ? '1' : '0'
    configForm.checkRescueTeam = value.includes('checkRescueTeam') ? '1' : '0'
    configForm.checkWarehouse = value.includes('checkWarehouse') ? '1' : '0'
    configForm.checkExpert = value.includes('checkExpert') ? '1' : '0'
  }
})

// 月份选项
const months = ref([
  { value: 1, label: '1月' },
  { value: 2, label: '2月' },
  { value: 3, label: '3月' },
  { value: 4, label: '4月' },
  { value: 5, label: '5月' },
  { value: 6, label: '6月' },
  { value: 7, label: '7月' },
  { value: 8, label: '8月' },
  { value: 9, label: '9月' },
  { value: 10, label: '10月' },
  { value: 11, label: '11月' },
  { value: 12, label: '12月' }
])

// 表单验证规则
const rules = {
  intervalMinutes: [
    { required: true, message: '请输入执行间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 1440, message: '执行间隔必须在1-1440分钟之间', trigger: 'blur' }
  ],
  seasonalPeriod: [
    {
      validator: (rule, value, callback) => {
        if (seasonalEnabled.value) {
          if (!configForm.startMonth || !configForm.endMonth) {
            callback(new Error('请选择季节性叫应时间段'))
          } else if (configForm.startMonth >= configForm.endMonth) {
            callback(new Error('开始月份必须小于结束月份'))
          }
        }
        callback()
      },
      trigger: 'change'
    }
  ]
}

// 方法
const loadConfig = async () => {
  try {
    const response = await getTaskConfig()
    if (response.code === 200) {
      Object.assign(configForm, response.data)
    }
  } catch (error) {
    ElMessage.error('获取配置失败')
  }
}

const handleSeasonalChange = (value) => {
  if (!value) {
    configForm.startMonth = null
    configForm.endMonth = null
  } else {
    configForm.startMonth = 3
    configForm.endMonth = 9
  }
}

const handleSave = async () => {
  try {
    await configFormRef.value.validate()
    loading.value = true
    
    const response = await updateTaskConfig(configForm)
    if (response.code === 200) {
      ElMessage.success('配置保存成功')
      await loadConfig() // 重新加载配置
    } else {
      ElMessage.error(response.msg || '配置保存失败')
    }
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    loading.value = false
  }
}

const handleRunNow = async () => {
  try {
    runNowLoading.value = true
    const response = await updateTaskConfig({ operation: 'runNow' })
    if (response.code === 200) {
      ElMessage.success('任务已立即执行')
    } else {
      ElMessage.error(response.msg || '执行失败')
    }
  } catch (error) {
    ElMessage.error('执行失败')
  } finally {
    runNowLoading.value = false
  }
}

const handleReset = async () => {
  try {
    await ElMessageBox.confirm('确定要重置为默认配置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await resetTaskConfig()
    if (response.code === 200) {
      ElMessage.success('重置成功')
      await loadConfig()
    } else {
      ElMessage.error(response.msg || '重置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败')
    }
  }
}

// 生命周期
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.info-timeout-task-config {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mx-2 {
  margin: 0 8px;
}

.text-gray-500 {
  color: #6b7280;
}

.text-blue-600 {
  color: #2563eb;
}
</style>
```

## 使用示例

### 1. 获取当前配置
```bash
GET /monitor/infoTimeoutTask/config
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "intervalMinutes": 30,
    "enableSeasonal": "1",
    "startMonth": 3,
    "endMonth": 9,
    "checkPrePlan": "1",
    "checkRescueTeam": "1",
    "checkWarehouse": "0",
    "checkExpert": "1",
    "prePlanTimeoutDays": 30,
    "rescueTeamTimeoutDays": 30,
    "warehouseTimeoutDays": 30,
    "expertTimeoutDays": 30,
    "status": "0",
    "remark": "应急信息更新超时检查任务配置 - 更新时间：2025-01-13 16:30:00"
  }
}
```

### 2. 完整配置更新
```bash
POST /monitor/infoTimeoutTask/config
Content-Type: application/json

{
  "intervalMinutes": 60,
  "startMonth": 4,
  "endMonth": 10,
  "enableSeasonal": "1",
  "checkPrePlan": "1",
  "checkRescueTeam": "1",
  "checkWarehouse": "0",
  "checkExpert": "1",
  "prePlanTimeoutDays": 15,
  "rescueTeamTimeoutDays": 45,
  "warehouseTimeoutDays": 60,
  "expertTimeoutDays": 20,
  "status": "0"
}
```

### 3. 只更新超时阈值
```bash
POST /monitor/infoTimeoutTask/config
Content-Type: application/json

{
  "prePlanTimeoutDays": 10,
  "rescueTeamTimeoutDays": 20,
  "warehouseTimeoutDays": 30,
  "expertTimeoutDays": 15
}
```

### 4. 立即执行任务
```bash
POST /monitor/infoTimeoutTask/config
Content-Type: application/json

{
  "operation": "runNow"
}
```

### 5. 启动/停止任务
```bash
# 启动任务
POST /monitor/infoTimeoutTask/config
Content-Type: application/json

{
  "status": "0"
}

# 停止任务
POST /monitor/infoTimeoutTask/config
Content-Type: application/json

{
  "status": "1"
}
```

## 使用说明

1. **获取配置**：页面加载时自动获取当前任务配置
2. **保存配置**：修改配置后点击"保存配置"按钮
3. **立即执行**：点击"立即执行一次"按钮可手动触发任务
4. **重置配置**：点击"重置为默认"按钮恢复默认设置

## 注意事项

1. **配置存储**：所有配置直接通过字段传递，不再依赖备注解析
2. **参数可选**：所有参数都是可选的，只传递需要修改的参数即可
3. **季节性叫应**：启用时，开始月份必须小于结束月份
4. **叫应内容**：至少需要选择一种叫应内容
5. **执行间隔**：范围为1-1440分钟（1分钟到24小时）
6. **超时阈值**：范围为1-365天，默认值为30天
7. **数据一致性**：配置更新后立即生效，定时任务会自动获取最新配置
