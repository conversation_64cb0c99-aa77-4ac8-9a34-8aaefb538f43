package com.tocc.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.common.exception.ServiceException;
import com.tocc.domain.vo.EmergencyEventRelationsVo;
import com.tocc.domain.vo.EmergencyEventVO;
import com.tocc.domain.vo.RescueTeamCircleVO;
import com.tocc.domain.vo.WarehouseCircleVO;
import com.tocc.em.mapper.EmPrePlanMapper;
import com.tocc.em.service.IEmPrePlanService;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.mapper.EmergencyEventMapper;
import com.tocc.service.IEmergencyEventRelationsService;
import com.tocc.service.IEmergencyEventService;
import com.tocc.service.IRescueCircleService;
import com.tocc.system.domain.vo.ExpertInfoVO;
import com.tocc.system.mapper.ExpertInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.emergency.mapper.EmergencyEventRelationsMapper;
import com.tocc.domain.entity.EmergencyEventRelations;



/**
 * 应急事件关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Slf4j
public class EmergencyEventRelationsServiceImpl implements IEmergencyEventRelationsService
{
    @Autowired
    private EmergencyEventRelationsMapper emergencyEventRelationsMapper;

    @Autowired
    private EmergencyEventMapper emergencyEventMapper;

    @Autowired
    private IEmPrePlanService emPrePlanService;

    @Autowired
    private ExpertInfoMapper expertInfoMapper;

    @Autowired
    private IRescueCircleService rescueCircleService;

    @Autowired
    private IEmergencyEventService emergencyEventService;

    /**
     * 查询应急事件关联
     * 
     * @param eventRelationsId 应急事件关联主键
     * @return 应急事件关联
     */
    @Override
    public EmergencyEventRelations selectEmergencyEventRelationsByEventRelationsId(String eventRelationsId)
    {
        return emergencyEventRelationsMapper.selectEmergencyEventRelationsByEventRelationsId(eventRelationsId);
    }

    /**
     * 查询应急事件关联列表
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 应急事件关联
     */
    @Override
    public List<EmergencyEventRelations> selectEmergencyEventRelationsList(EmergencyEventRelations emergencyEventRelations)
    {
        return emergencyEventRelationsMapper.selectEmergencyEventRelationsList(emergencyEventRelations);
    }

    /**
     * 新增应急事件关联
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 结果
     */
    @Override
    public int insertEmergencyEventRelations(EmergencyEventRelations emergencyEventRelations)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        emergencyEventRelations.setCreator(loginUser.getUsername());
        emergencyEventRelations.setUpdater(loginUser.getUsername());
        emergencyEventRelations.setEventRelationsId(IdUtils.fastSimpleUUID());
        return emergencyEventRelationsMapper.insertEmergencyEventRelations(emergencyEventRelations);
    }

    /**
     * 修改应急事件关联
     * 
     * @param emergencyEventRelations 应急事件关联
     * @return 结果
     */
    @Override
    public int updateEmergencyEventRelations(EmergencyEventRelations emergencyEventRelations)
    {
        return emergencyEventRelationsMapper.updateEmergencyEventRelations(emergencyEventRelations);
    }

    /**
     * 批量删除应急事件关联
     * 
     * @param eventRelationsIds 需要删除的应急事件关联主键
     * @return 结果
     */
    @Override
    public int deleteEmergencyEventRelationsByEventRelationsIds(String[] eventRelationsIds)
    {
        // 参数校验
        if (eventRelationsIds == null || eventRelationsIds.length == 0) {
            log.warn("批量删除应急事件关联信息失败，eventRelationsIds为空");
            throw new ServiceException("删除失败：应急事件关联ID列表不能为空");
        }

        // 检查是否有空值或无效ID
        for (String id : eventRelationsIds) {
            if (id == null || id.trim().isEmpty()) {
                log.warn("批量删除应急事件关联信息失败，包含无效的ID: {}", id);
                throw new ServiceException("删除失败：包含无效的应急事件关联ID");
            }
        }

        try {
            int result = emergencyEventRelationsMapper.deleteEmergencyEventRelationsByEventRelationsIds(eventRelationsIds);
            if (result > 0) {
                log.info("成功批量删除应急事件关联信息，删除数量: {}，请求删除数量: {}", result, eventRelationsIds.length);
                if (result < eventRelationsIds.length) {
                    log.warn("部分数据删除失败，预期删除: {}，实际删除: {}", eventRelationsIds.length, result);
                }
                return result;
            } else {
                log.warn("批量删除应急事件关联信息失败，没有数据被删除");
                throw new ServiceException("删除失败：没有找到可删除的数据，可能数据已被删除或不存在");
            }
        } catch (Exception e) {
            if (e instanceof ServiceException) {
                throw e; // 重新抛出ServiceException
            }
            log.error("批量删除应急事件关联信息时发生异常: eventRelationsIds={}", eventRelationsIds, e);
            throw new ServiceException("删除失败：系统内部错误，请稍后重试或联系系统管理员");
        }
    }

    /**
     * 删除应急事件关联信息
     * 
     * @param eventRelationsId 应急事件关联主键
     * @return 结果
     */
    @Override
    public int deleteEmergencyEventRelationsByEventRelationsId(String eventRelationsId)
    {
        // 参数校验
        if (eventRelationsId == null || eventRelationsId.trim().isEmpty()) {
            log.warn("删除应急事件关联信息失败，eventRelationsId为空");
            throw new ServiceException("删除失败：应急事件关联ID不能为空");
        }

        // 先查询数据是否存在
        EmergencyEventRelations existingRelation = emergencyEventRelationsMapper.selectEmergencyEventRelationsByEventRelationsId(eventRelationsId);
        if (existingRelation == null) {
            log.warn("删除应急事件关联信息失败，未找到对应的数据: eventRelationsId={}", eventRelationsId);
            throw new ServiceException("删除失败：未找到指定的应急事件关联信息，可能已被删除或不存在");
        }

        // 检查是否已经被删除（逻辑删除）
        if (existingRelation.getDelFlag() != null && existingRelation.getDelFlag() == 1) {
            log.warn("应急事件关联信息已被删除，无需重复操作: eventRelationsId={}", eventRelationsId);
            throw new ServiceException("删除失败：该应急事件关联信息已被删除，无需重复操作");
        }

        // 执行删除操作
        try {
            int result = emergencyEventRelationsMapper.deleteEmergencyEventRelationsByEventRelationsId(eventRelationsId);
            if (result > 0) {
                log.info("成功删除应急事件关联信息: eventRelationsId={}", eventRelationsId);
                return result;
            } else {
                log.error("删除应急事件关联信息失败，数据库操作未生效: eventRelationsId={}", eventRelationsId);
                throw new ServiceException("删除失败：数据库操作异常，请联系系统管理员");
            }
        } catch (Exception e) {
            if (e instanceof ServiceException) {
                throw e; // 重新抛出ServiceException
            }
            log.error("删除应急事件关联信息时发生异常: eventRelationsId={}", eventRelationsId, e);
            throw new ServiceException("删除失败：系统内部错误，请稍后重试或联系系统管理员");
        }
    }

    @Override
    public EmergencyEventRelationsVo getInfoByEventId(String eventId) {
        // 参数校验
        if (eventId == null || eventId.trim().isEmpty()) {
            return null;
        }

        // 1. 查询应急事件关联信息
        EmergencyEventRelations query = new EmergencyEventRelations();
        query.setEventId(eventId);
        List<EmergencyEventRelations> relationsList = emergencyEventRelationsMapper.selectEmergencyEventRelationsList(query);
        if (relationsList == null || relationsList.isEmpty()) {
            return null;
        }
        EmergencyEventRelations relations = relationsList.get(0);

        // 2. 构建返回对象
        EmergencyEventRelationsVo result = new EmergencyEventRelationsVo();
        result.setEventRelationsId(relations.getEventRelationsId());
        result.setCreator(relations.getCreator());
        result.setUpdater(relations.getUpdater());
        result.setCreateTime(relations.getCreateTime());
        result.setUpdateTime(relations.getUpdateTime());
        result.setDelFlag(relations.getDelFlag());

        try {
            // 3. 查询应急事件信息
            if (relations.getEventId() != null) {
                EmergencyEventVO eventVO = emergencyEventService.selectEmergencyEventByEventId(relations.getEventId());
                result.setEmergencyEventVO(eventVO);
            }

            // 4. 查询应急预案信息
            if (relations.getPrePlanId() != null && !relations.getPrePlanId().trim().isEmpty()) {
                EmPrePlanVO prePlanVO = emPrePlanService.selectEmPrePlanById(relations.getPrePlanId());
                result.setEmPrePlanVO(prePlanVO);
            }

            // 5. 查询推荐专家
            if (relations.getExpertInfoIds() != null && !relations.getExpertInfoIds().trim().isEmpty()) {
                String[] expertIds = relations.getExpertInfoIds().split(",");
                List<ExpertInfoVO> expertList = new ArrayList<>();
                for (String expertId : expertIds) {
                    if (expertId != null && !expertId.trim().isEmpty()) {
                        ExpertInfoVO expert = expertInfoMapper.selectExpertInfoById(expertId.trim());
                        if (expert != null) {
                            expertList.add(expert);
                        }
                    }
                }
                result.setExpertInfoVOList(expertList);
            }

            // 6. 查询应急物资信息
            if (relations.getWarehouseId() != null && !relations.getWarehouseId().trim().isEmpty()) {
                WarehouseCircleVO warehouseVO = rescueCircleService.getWarehouseCircleByEventId(eventId);
                result.setWarehouseCircleVO(warehouseVO);
            }

            // 7. 查询救援队伍信息
            if (relations.getRescueTeamId() != null && !relations.getRescueTeamId().trim().isEmpty()) {
                RescueTeamCircleVO rescueTeamVO = rescueCircleService.getRescueTeamCircleByEventId(eventId);
                result.setRescueTeamCircleVO(rescueTeamVO);
            }

        } catch (Exception e) {
            // 记录日志但不影响主流程
             log.error("查询应急事件关联信息时发生异常: eventId={}", eventId, e);
        }

        return result;
    }
}
