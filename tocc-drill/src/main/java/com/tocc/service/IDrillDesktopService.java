package com.tocc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tocc.domain.dto.DrillDesktopDto;
import com.tocc.domain.entity.DrillDesktop;
import com.tocc.domain.vo.DrillDesktopDetailVo;
import com.tocc.domain.vo.DrillDesktopVo;

import java.util.List;

public interface IDrillDesktopService extends IService<DrillDesktop> {

    Boolean savePlan(DrillDesktopDto dto);

    DrillDesktopDetailVo detail(Long id);

    List<DrillDesktopVo> list(DrillDesktopDto dto);
}
