package com.tocc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.domain.dto.DrillDesktopDto;
import com.tocc.domain.entity.DrillDesktop;
import com.tocc.domain.entity.DrillDesktopTask;
import com.tocc.domain.vo.DrillDesktopDetailVo;
import com.tocc.domain.vo.DrillDesktopVo;
import com.tocc.mapper.DrillDesktopMapper;
import com.tocc.service.IDrillDesktopService;
import com.tocc.service.IDrillDesktopTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
public class DrillDesktopServiceImpl extends ServiceImpl<DrillDesktopMapper, DrillDesktop> implements IDrillDesktopService {
    @Autowired
    private IDrillDesktopTaskService drillDesktopTaskService;

    @Autowired
    private DrillDesktopMapper drillDesktopMapper;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean savePlan(DrillDesktopDto dto) {
        DrillDesktop drillDesktop = BeanUtil.copyProperties(dto, DrillDesktop.class);
        if (ObjectUtil.isNotNull(drillDesktop.getId())){
            this.updateById(drillDesktop);
        }else {
            drillDesktop.setCreateBy(SecurityUtils.getUsername());
            this.save(drillDesktop);
        }
        if (CollectionUtil.isNotEmpty(dto.getDrillDesktopTasks())){
            dto.getDrillDesktopTasks().forEach(item ->{
                item.setDrillDesktopId(drillDesktop.getId());
            });
            drillDesktopTaskService.saveTaskList(dto.getDrillDesktopTasks());
        }
        return true;
    }

    @Override
    public DrillDesktopDetailVo detail(Long id) {
        List<DrillDesktop> list = this.listByIds(Collections.singleton(id));
        if (CollectionUtil.isNotEmpty(list)){
            DrillDesktopDetailVo drillDesktopDetailVo = BeanUtil.copyProperties(list.get(0), DrillDesktopDetailVo.class);
            drillDesktopDetailVo.setDrillDesktopTasks(drillDesktopTaskService.listByBizId(id));
            return drillDesktopDetailVo;
        }
        return null;
    }

    @Override
    public List<DrillDesktopVo> list(DrillDesktopDto dto) {

        return drillDesktopMapper.list(dto);
    }
}
