package com.tocc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.domain.dto.DrillReminderDto;
import com.tocc.domain.entity.DrillPlan;
import com.tocc.domain.entity.DrillReminder;
import com.tocc.domain.vo.DrillRemainderVo;
import com.tocc.mapper.DrillReminderMapper;
import com.tocc.service.IDrillPlanService;
import com.tocc.service.IDrillReminderService;
import com.tocc.service.IEmergencyEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DrillReminderServiceImpl extends ServiceImpl<DrillReminderMapper, DrillReminder> implements IDrillReminderService {

    @Autowired
    private IDrillPlanService drillPlanService;

    @Autowired
    private IEmergencyEventService emergencyEventService;

    @Override
    public Boolean saveEntity(DrillReminderDto dto) {


        DrillReminder drillReminder = BeanUtil.copyProperties(dto, DrillReminder.class);

        if (ObjectUtil.isNotNull(drillReminder.getId())){
            this.updateById(drillReminder);
        }else {
            drillReminder.setCreateBy(SecurityUtils.getUsername());
            this.save(drillReminder);
        }
        List<DrillPlan> drillPlans = drillPlanService.listByIds(Collections.singleton(dto.getDrillPlanId()));
        if (CollectionUtil.isNotEmpty(drillPlans)){
            DrillPlan drillPlan = drillPlans.get(0);

//            emergencyEventService.sendMsg("",drillPlan.getPhone());
        }

        return true;

    }

    @Override
    public DrillRemainderVo detail(Long drillPlanId) {
        List<DrillPlan> drillPlans = drillPlanService.listByIds(Collections.singleton(drillPlanId));

        if (CollectionUtil.isNotEmpty(drillPlans)){
            DrillPlan drillPlan = drillPlans.get(0);
            DrillRemainderVo drillRemainderVo = BeanUtil.copyProperties(drillPlan, DrillRemainderVo.class);
            drillRemainderVo.setDrillPlanId(drillPlan.getId());
            drillRemainderVo.setId(null);

            QueryWrapper<DrillReminder> qw = new QueryWrapper<>();
            qw.eq("drill_plan_id",drillPlanId);
            List<DrillReminder> list = this.list(qw);
            if (CollectionUtil.isNotEmpty(list)){
                drillRemainderVo.setId(list.get(0).getId());
                drillRemainderVo.setIsDrill(list.get(0).getIsDrill());
                drillRemainderVo.setIsReview(list.get(0).getIsReview());
                drillRemainderVo.setDeadline(list.get(0).getDeadline());
            }
            return drillRemainderVo;
        }
        return null;
    }
}
